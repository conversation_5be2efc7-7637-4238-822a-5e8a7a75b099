import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { Client, ClientOptions } from "minio";
import { z } from "zod";

export interface FileInfo {
    originalname: string;
    buffer: Buffer;
    mimetype: string;
    size: number;
}

@Injectable()
export class MinioService {
    private readonly client: Client;
    private readonly logger = new Logger(MinioService.name);
    private readonly buckets = {
        communeImages: "commune-images",
        userImages: "user-images",
        postImages: "post-images",
    };

    constructor(private readonly configService: ConfigService) {
        const endPoint = z
            .string()
            .nonempty()
            .parse(this.configService.get("MINIO_ENDPOINT"));
        const port = z.coerce
            .number()
            .int()
            .positive()
            .parse(this.configService.get("MINIO_PORT"));
        const accessKey = z
            .string()
            .nonempty()
            .parse(this.configService.get("MINIO_ACCESS_KEY"));
        const secretKey = z
            .string()
            .nonempty()
            .parse(this.configService.get("MINIO_SECRET_KEY"));
        const useSSL = z.coerce
            .boolean()
            .default(false)
            .parse(this.configService.get("MINIO_USE_SSL"));

        const options: ClientOptions = {
            endPoint,
            port,
            useSSL,
            accessKey,
            secretKey,
        };

        this.client = new Client(options);
        this.initializeBuckets();
    }

    private async initializeBuckets(): Promise<void> {
        await Promise.all(
            Object.values(this.buckets).map(async (bucket) => {
                const exists = await this.client.bucketExists(bucket);

                if (!exists) {
                    await this.client.makeBucket(bucket, "us-east-1");

                    this.logger.log(`Bucket '${bucket}' created successfully`);

                    // Set bucket policy to allow public read access
                    const policy = {
                        Version: "2012-10-17",
                        Statement: [
                            {
                                Effect: "Allow",
                                Principal: { AWS: ["*"] },
                                Action: ["s3:GetObject"],
                                Resource: [`arn:aws:s3:::${bucket}/*`],
                            },
                        ],
                    };

                    await this.client.setBucketPolicy(
                        bucket,
                        JSON.stringify(policy),
                    );

                    this.logger.log(
                        `Public read policy set for bucket '${bucket}'`,
                    );
                }
            }),
        ).catch((error) => {
            this.logger.error("Failed to initialize MinIO buckets", error);
        });
    }

    async uploadFile(
        file: FileInfo,
        bucket: string,
        objectName: string,
    ): Promise<string> {
        try {
            await this.client.putObject(
                bucket,
                objectName,
                file.buffer,
                undefined,
                {
                    "Content-Type": file.mimetype,
                },
            );

            return `${bucket}/${objectName}`;
        } catch (error) {
            this.logger.error(
                `Failed to upload file ${objectName} to bucket ${bucket}`,
                error,
            );

            throw error;
        }
    }

    async uploadCommuneImage(
        file: FileInfo,
        communeId: string,
        index: number,
    ): Promise<string> {
        const fileExt = file.originalname.split(".").pop();
        const objectName = `${communeId}/${index}.${fileExt}`;

        return await this.uploadFile(
            file,
            this.buckets.communeImages,
            objectName,
        );
    }

    async uploadUserImage(
        file: FileInfo,
        userId: string,
        index: number,
    ): Promise<string> {
        const fileExt = file.originalname.split(".").pop();
        const objectName = `${userId}/${index}.${fileExt}`;

        return await this.uploadFile(file, this.buckets.userImages, objectName);
    }

    async deleteFile(bucket: string, objectName: string): Promise<void> {
        try {
            await this.client.removeObject(bucket, objectName);
        } catch (error) {
            this.logger.error(
                `Failed to delete file ${objectName} from bucket ${bucket}`,
                error,
            );

            throw error;
        }
    }

    async deleteCommuneImage(
        communeId: string,
        filename: string,
    ): Promise<void> {
        const objectName = `${communeId}/${filename}`;

        return await this.deleteFile(this.buckets.communeImages, objectName);
    }

    async deleteUserImage(userId: string, filename: string): Promise<void> {
        const objectName = `${userId}/${filename}`;

        return await this.deleteFile(this.buckets.userImages, objectName);
    }

    async uploadPostImage(
        file: FileInfo,
        postId: string,
        index: number,
    ): Promise<string> {
        const fileExt = file.originalname.split(".").pop();
        const objectName = `${postId}/${index}.${fileExt}`;

        return await this.uploadFile(file, this.buckets.postImages, objectName);
    }

    async deletePostImage(postId: string, filename: string): Promise<void> {
        const objectName = `${postId}/${filename}`;

        return await this.deleteFile(this.buckets.postImages, objectName);
    }
}
