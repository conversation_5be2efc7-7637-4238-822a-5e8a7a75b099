import type { Common } from "@commune/api";
import type { CurrentUser } from "src/auth/types";

import { ForbiddenException, Injectable, Logger } from "@nestjs/common";
import { toPrismaLocalizations, toPrismaPagination } from "src/utils";
import { PrismaService } from "src/prisma/prisma.service";
import { getError } from "src/common/errors";

@Injectable()
export class RatingService {
    private readonly logger = new Logger(RatingService.name);

    constructor(private readonly prisma: PrismaService) {}

    async getUserSummary(userId: string) {
        const [rating, karma, rate] = await Promise.all([
            this.prisma.userRating.aggregate({
                where: {
                    targetUserId: userId,
                },
                _sum: {
                    value: true,
                },
            }),

            this.prisma.userKarmaGivenPoint.aggregate({
                where: {
                    targetUserId: userId,
                },
                _sum: {
                    quantity: true,
                },
            }),

            this.prisma.userFeedback.aggregate({
                where: {
                    targetUserId: userId,
                },
                _avg: {
                    value: true,
                },
            }),
        ]);

        return {
            rating: rating._sum.value ?? 0,
            karma: karma._sum.quantity ?? 0,
            rate: rate._avg.value,
        };
    }

    async getKarmaPoints(userId: string, pagination?: Common.Pagination) {
        return await this.prisma.userKarmaGivenPoint.findMany({
            ...toPrismaPagination(pagination),
            where: {
                targetUserId: userId,
            },
            include: {
                comment: true,
                sourceUser: {
                    include: {
                        name: true,
                        images: {
                            take: 1,
                        },
                    },
                },
            },
        });
    }

    async spendKarmaPoint(
        data: {
            sourceUserId: string;
            targetUserId: string;
            quantity: number;
            comment: Common.Localizations;
        },
        user: CurrentUser,
    ) {
        if (!user.isAdmin) {
            if (user.id !== data.sourceUserId) {
                throw new ForbiddenException(
                    getError("must_be_admin_or_source_user"),
                );
            }

            if (data.sourceUserId === data.targetUserId) {
                throw new ForbiddenException(
                    getError("source_and_target_users_must_differ"),
                );
            }

            if (data.quantity !== 1 && data.quantity !== -1) {
                throw new ForbiddenException(
                    getError("must_be_admin_or_use_only_one_point"),
                );
            }
        }

        const id = await this.prisma.$transaction(async (trx) => {
            let userKarmaSpendablePointId: string | null = null;
            let availablePoints = Number.MAX_SAFE_INTEGER;

            if (!user.isAdmin) {
                const userKarmaSpendablePoint =
                    await trx.userKarmaSpendablePoint.findUnique({
                        where: {
                            userId: data.sourceUserId,
                        },
                    });

                if (!userKarmaSpendablePoint) {
                    throw new ForbiddenException(
                        ...getError("must_have_at_least_one_spendable_point"),
                    );
                }

                userKarmaSpendablePointId = userKarmaSpendablePoint.id;
                availablePoints = userKarmaSpendablePoint.points;
            }

            if (availablePoints < data.quantity) {
                throw new ForbiddenException(
                    ...getError("must_have_at_least_one_spendable_point"),
                );
            }

            const { id } = await trx.userKarmaGivenPoint.create({
                data: {
                    sourceUserId: data.sourceUserId,
                    targetUserId: data.targetUserId,
                    quantity: data.quantity,
                    comment: {
                        create: toPrismaLocalizations(data.comment, "comment"),
                    },
                },
            });

            if (userKarmaSpendablePointId) {
                await trx.userKarmaSpendablePoint.update({
                    where: {
                        id: userKarmaSpendablePointId,
                    },
                    data: {
                        points: {
                            decrement: data.quantity,
                        },
                    },
                });
            }

            return id;
        });

        return { id };
    }

    async getUserFeedbacks(userId: string, pagination?: Common.Pagination) {
        return await this.prisma.userFeedback.findMany({
            ...toPrismaPagination(pagination),
            where: {
                targetUserId: userId,
            },
            include: {
                text: true,
                sourceUser: {
                    include: {
                        name: true,
                        images: {
                            take: 1,
                        },
                    },
                },
            },
        });
    }

    async createUserFeedback(
        data: {
            sourceUserId: string;
            targetUserId: string;
            value: number;
            isAnonymous: boolean;
            text: Common.Localizations;
        },
        user: CurrentUser,
    ) {
        if (!user.isAdmin) {
            if (user.id !== data.sourceUserId) {
                throw new ForbiddenException(
                    getError("must_be_admin_or_source_user"),
                );
            }

            if (data.sourceUserId === data.targetUserId) {
                throw new ForbiddenException(
                    getError("source_and_target_users_must_differ"),
                );
            }
        }

        const { id } = await this.prisma.userFeedback.create({
            data: {
                sourceUserId: data.sourceUserId,
                targetUserId: data.targetUserId,
                value: data.value,
                isAnonymous: data.isAnonymous,
                text: {
                    create: toPrismaLocalizations(data.text, "text"),
                },
            },
        });

        return { id };
    }
}
