import { z } from 'zod';

type NonNullableUnknown = NonNullable<unknown>;

type SuggestableString<T extends string> =
    | T
    | (string & NonNullableUnknown);

type Satisfies<T, U extends T> = U;

type Normalize<T> = { [K in keyof T]: T[K] } & NonNullableUnknown;

type Infer<T extends z.ZodTypeAny> = Normalize<z.infer<T>>;

type InferObject<T extends Record<string, z.ZodType>> = Normalize<{
    [K in keyof T]: z.infer<T[K]>;
}>;

type types_d_Infer<T extends z.ZodTypeAny> = Infer<T>;
type types_d_InferObject<T extends Record<string, z.ZodType>> = InferObject<T>;
type types_d_NonNullableUnknown = NonNullableUnknown;
type types_d_Normalize<T> = Normalize<T>;
type types_d_Satisfies<T, U extends T> = Satisfies<T, U>;
type types_d_SuggestableString<T extends string> = SuggestableString<T>;
declare namespace types_d {
  export type { types_d_Infer as Infer, types_d_InferObject as InferObject, types_d_NonNullableUnknown as NonNullableUnknown, types_d_Normalize as Normalize, types_d_Satisfies as Satisfies, types_d_SuggestableString as SuggestableString };
}

declare const id: z.ZodString;
declare const email: z.ZodString;
declare const stringToDate: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
declare function JsonStringToObject<T extends z.ZodRawShape>(schema: T): z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<T, "strip", z.ZodTypeAny, z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any> extends infer T_1 ? { [k in keyof T_1]: z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any>[k]; } : never, z.baseObjectInputType<T> extends infer T_2 ? { [k_1 in keyof T_2]: z.baseObjectInputType<T>[k_1]; } : never>>;
declare function FormDataToObject<T extends z.ZodRawShape>(schema: T): z.ZodObject<{
    data: z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<T, "strip", z.ZodTypeAny, z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any> extends infer T_1 ? { [k in keyof T_1]: z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any>[k]; } : never, z.baseObjectInputType<T> extends infer T_2 ? { [k_1 in keyof T_2]: z.baseObjectInputType<T>[k_1]; } : never>>;
}, "strip", z.ZodTypeAny, z.objectUtil.addQuestionMarks<z.baseObjectOutputType<{
    data: z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<T, "strip", z.ZodTypeAny, z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any> extends infer T_6 ? { [k in keyof T_6]: z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any>[k]; } : never, z.baseObjectInputType<T> extends infer T_7 ? { [k_1 in keyof T_7]: z.baseObjectInputType<T>[k_1]; } : never>>;
}>, any> extends infer T_3 ? { [k_2 in keyof T_3]: z.objectUtil.addQuestionMarks<z.baseObjectOutputType<{
    data: z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<T, "strip", z.ZodTypeAny, z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any> extends infer T_4 ? { [k in keyof T_4]: z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any>[k]; } : never, z.baseObjectInputType<T> extends infer T_5 ? { [k_1 in keyof T_5]: z.baseObjectInputType<T>[k_1]; } : never>>;
}>, any>[k_2]; } : never, {
    data: string;
}>;
type ObjectWithId = Infer<typeof ObjectWithIdSchema>;
declare const ObjectWithIdSchema: z.ZodObject<{
    id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
}, {
    id: string;
}>;
type WebsiteLocale = Infer<typeof WebsiteLocaleSchema>;
declare const WebsiteLocaleSchema: z.ZodEnum<["en", "ru"]>;
type LocalizationLocale = Infer<typeof LocalizationLocaleSchema>;
declare const LocalizationLocaleSchema: z.ZodEnum<["en", "ru"]>;
type LocalizationLocales = Infer<typeof LocalizationLocalesSchema>;
declare const LocalizationLocalesSchema: z.ZodArray<z.ZodEnum<["en", "ru"]>, "many">;
type Localization = Infer<typeof LocalizationSchema>;
declare const LocalizationSchema: z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>;
type Localizations = Infer<typeof LocalizationsSchema>;
declare const LocalizationsSchema: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
type Image = Infer<typeof ImageSchema>;
declare const ImageSchema: z.ZodObject<{
    id: z.ZodString;
    url: z.ZodString;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    url: string;
    createdAt: Date;
    updatedAt: Date;
}, {
    id: string;
    url: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
}>;
type Images = Infer<typeof ImagesSchema>;
declare const ImagesSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    url: z.ZodString;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    url: string;
    createdAt: Date;
    updatedAt: Date;
}, {
    id: string;
    url: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
}>, "many">;
declare const pagination: {
    offset: z.ZodDefault<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
    page: z.ZodDefault<z.ZodNumber>;
    size: z.ZodDefault<z.ZodNumber>;
};
type Pagination = Infer<typeof PaginationSchema>;
declare const PaginationSchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodNumber>;
    size: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    page: number;
    size: number;
}, {
    page?: number | undefined;
    size?: number | undefined;
}>;
declare function parseInput<T extends z.ZodTypeAny>(schema: T, value: z.input<T>): z.output<T>;
declare function parseUnknown<T extends z.ZodTypeAny>(schema: T, value: unknown): z.output<T>;

declare const common_FormDataToObject: typeof FormDataToObject;
type common_Image = Image;
declare const common_ImageSchema: typeof ImageSchema;
type common_Images = Images;
declare const common_ImagesSchema: typeof ImagesSchema;
declare const common_JsonStringToObject: typeof JsonStringToObject;
type common_Localization = Localization;
type common_LocalizationLocale = LocalizationLocale;
declare const common_LocalizationLocaleSchema: typeof LocalizationLocaleSchema;
type common_LocalizationLocales = LocalizationLocales;
declare const common_LocalizationLocalesSchema: typeof LocalizationLocalesSchema;
declare const common_LocalizationSchema: typeof LocalizationSchema;
type common_Localizations = Localizations;
declare const common_LocalizationsSchema: typeof LocalizationsSchema;
type common_ObjectWithId = ObjectWithId;
declare const common_ObjectWithIdSchema: typeof ObjectWithIdSchema;
type common_Pagination = Pagination;
declare const common_PaginationSchema: typeof PaginationSchema;
type common_WebsiteLocale = WebsiteLocale;
declare const common_WebsiteLocaleSchema: typeof WebsiteLocaleSchema;
declare const common_email: typeof email;
declare const common_id: typeof id;
declare const common_pagination: typeof pagination;
declare const common_parseInput: typeof parseInput;
declare const common_parseUnknown: typeof parseUnknown;
declare const common_stringToDate: typeof stringToDate;
declare namespace common {
  export { common_FormDataToObject as FormDataToObject, type common_Image as Image, common_ImageSchema as ImageSchema, type common_Images as Images, common_ImagesSchema as ImagesSchema, common_JsonStringToObject as JsonStringToObject, type common_Localization as Localization, type common_LocalizationLocale as LocalizationLocale, common_LocalizationLocaleSchema as LocalizationLocaleSchema, type common_LocalizationLocales as LocalizationLocales, common_LocalizationLocalesSchema as LocalizationLocalesSchema, common_LocalizationSchema as LocalizationSchema, type common_Localizations as Localizations, common_LocalizationsSchema as LocalizationsSchema, type common_ObjectWithId as ObjectWithId, common_ObjectWithIdSchema as ObjectWithIdSchema, type common_Pagination as Pagination, common_PaginationSchema as PaginationSchema, type common_WebsiteLocale as WebsiteLocale, common_WebsiteLocaleSchema as WebsiteLocaleSchema, common_email as email, common_id as id, common_pagination as pagination, common_parseInput as parseInput, common_parseUnknown as parseUnknown, common_stringToDate as stringToDate };
}

declare const otp: z.ZodString;
type SendOtpRequest = Infer<typeof SendOtpRequestSchema>;
declare const SendOtpRequestSchema: z.ZodObject<{
    email: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
}, {
    email: string;
}>;
type SendOtpResponse = Infer<typeof SendOtpResponseSchema>;
declare const SendOtpResponseSchema: z.ZodObject<{
    isSent: z.ZodBoolean;
}, "strip", z.ZodTypeAny, {
    isSent: boolean;
}, {
    isSent: boolean;
}>;
type GetMeResponse = Infer<typeof GetMeResponseSchema>;
declare const GetMeResponseSchema: z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    role: z.ZodEnum<["admin", "moderator", "user"]>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    images: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }, {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }>, "many">;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    email: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    images: {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }[];
    role: "admin" | "moderator" | "user";
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    email: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    images: {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }[];
    role: "admin" | "moderator" | "user";
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
}>;
type RegisterRequest = Infer<typeof RegisterRequestSchema>;
declare const RegisterRequestSchema: z.ZodObject<{
    referrerId: z.ZodNullable<z.ZodString>;
    email: z.ZodString;
    otp: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
    referrerId: string | null;
    otp: string;
}, {
    email: string;
    referrerId: string | null;
    otp: string;
}>;
type LoginRequest = Infer<typeof LoginRequestSchema>;
declare const LoginRequestSchema: z.ZodObject<{
    email: z.ZodString;
    otp: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
    otp: string;
}, {
    email: string;
    otp: string;
}>;
type SuccessfulAuthResponse = Infer<typeof SuccessfulAuthResponseSchema>;
declare const SuccessfulAuthResponseSchema: z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    role: z.ZodEnum<["admin", "moderator", "user"]>;
}, "strip", z.ZodTypeAny, {
    id: string;
    email: string;
    role: "admin" | "moderator" | "user";
}, {
    id: string;
    email: string;
    role: "admin" | "moderator" | "user";
}>;

type auth_GetMeResponse = GetMeResponse;
declare const auth_GetMeResponseSchema: typeof GetMeResponseSchema;
type auth_LoginRequest = LoginRequest;
declare const auth_LoginRequestSchema: typeof LoginRequestSchema;
type auth_RegisterRequest = RegisterRequest;
declare const auth_RegisterRequestSchema: typeof RegisterRequestSchema;
type auth_SendOtpRequest = SendOtpRequest;
declare const auth_SendOtpRequestSchema: typeof SendOtpRequestSchema;
type auth_SendOtpResponse = SendOtpResponse;
declare const auth_SendOtpResponseSchema: typeof SendOtpResponseSchema;
type auth_SuccessfulAuthResponse = SuccessfulAuthResponse;
declare const auth_SuccessfulAuthResponseSchema: typeof SuccessfulAuthResponseSchema;
declare const auth_otp: typeof otp;
declare namespace auth {
  export { type auth_GetMeResponse as GetMeResponse, auth_GetMeResponseSchema as GetMeResponseSchema, type auth_LoginRequest as LoginRequest, auth_LoginRequestSchema as LoginRequestSchema, type auth_RegisterRequest as RegisterRequest, auth_RegisterRequestSchema as RegisterRequestSchema, type auth_SendOtpRequest as SendOtpRequest, auth_SendOtpRequestSchema as SendOtpRequestSchema, type auth_SendOtpResponse as SendOtpResponse, auth_SendOtpResponseSchema as SendOtpResponseSchema, type auth_SuccessfulAuthResponse as SuccessfulAuthResponse, auth_SuccessfulAuthResponseSchema as SuccessfulAuthResponseSchema, auth_otp as otp };
}

type CommuneMemberType = Infer<typeof CommuneMemberTypeSchema>;
declare const CommuneMemberTypeSchema: z.ZodEnum<["user"]>;
declare const communeMemberActorType: z.ZodEnum<["user"]>;
declare const communeMemberName: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
type CommuneMember = Infer<typeof CommuneMemberSchema>;
declare const CommuneMemberSchema: z.ZodObject<{
    id: z.ZodString;
    actorType: z.ZodEnum<["user"]>;
    actorId: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    images: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }, {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }>, "many">;
    joinedAt: z.ZodDate;
    leftAt: z.ZodNullable<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    images: {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }[];
    actorType: "user";
    actorId: string;
    joinedAt: Date;
    leftAt: Date | null;
}, {
    id: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    images: {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }[];
    actorType: "user";
    actorId: string;
    joinedAt: Date;
    leftAt: Date | null;
}>;
type CommuneMembers = Infer<typeof CommuneMembersSchema>;
declare const CommuneMembersSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    actorType: z.ZodEnum<["user"]>;
    actorId: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    images: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }, {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }>, "many">;
    joinedAt: z.ZodDate;
    leftAt: z.ZodNullable<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    images: {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }[];
    actorType: "user";
    actorId: string;
    joinedAt: Date;
    leftAt: Date | null;
}, {
    id: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    images: {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }[];
    actorType: "user";
    actorId: string;
    joinedAt: Date;
    leftAt: Date | null;
}>, "many">;
declare const communeName: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
declare const communeDescription: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
type Commune = Infer<typeof CommuneSchema>;
declare const CommuneSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    headMember: z.ZodObject<{
        actorType: z.ZodEnum<["user"]>;
        actorId: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        actorType: "user";
        actorId: string;
    }, {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        actorType: "user";
        actorId: string;
    }>;
    memberCount: z.ZodNumber;
    images: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }, {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }>, "many">;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    images: {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headMember: {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        actorType: "user";
        actorId: string;
    };
    memberCount: number;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    images: {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headMember: {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        actorType: "user";
        actorId: string;
    };
    memberCount: number;
}>;
type Communes = Infer<typeof CommunesSchema>;
declare const CommunesSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    headMember: z.ZodObject<{
        actorType: z.ZodEnum<["user"]>;
        actorId: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        actorType: "user";
        actorId: string;
    }, {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        actorType: "user";
        actorId: string;
    }>;
    memberCount: z.ZodNumber;
    images: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }, {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }>, "many">;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    images: {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headMember: {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        actorType: "user";
        actorId: string;
    };
    memberCount: number;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    images: {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headMember: {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        actorType: "user";
        actorId: string;
    };
    memberCount: number;
}>, "many">;
type CreateCommuneRequest = Infer<typeof CreateCommuneRequestSchema>;
declare const CreateCommuneRequestSchema: z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<{
    headUserId: z.ZodOptional<z.ZodString>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headUserId?: string | undefined;
}, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headUserId?: string | undefined;
}>>;
type UpdateCommuneRequest = Infer<typeof UpdateCommuneRequestSchema>;
declare const UpdateCommuneRequestSchema: z.ZodObject<{
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
}, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
}>;
type CreateCommuneMemberRequest = Infer<typeof CreateCommuneMemberRequestSchema>;
declare const CreateCommuneMemberRequestSchema: z.ZodObject<{
    userId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    userId: string;
}, {
    userId: string;
}>;
type CommuneInvitationStatus = Infer<typeof CommuneInvitationStatusSchema>;
declare const CommuneInvitationStatusSchema: z.ZodEnum<["pending", "accepted", "rejected", "expired"]>;
type CommuneInvitation = Infer<typeof CommuneInvitationSchema>;
declare const CommuneInvitationSchema: z.ZodObject<{
    id: z.ZodString;
    communeId: z.ZodString;
    userId: z.ZodString;
    status: z.ZodEnum<["pending", "accepted", "rejected", "expired"]>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    status: "pending" | "accepted" | "rejected" | "expired";
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    communeId: string;
}, {
    status: "pending" | "accepted" | "rejected" | "expired";
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    communeId: string;
}>;
type CommuneInvitations = Infer<typeof CommuneInvitationsSchema>;
declare const CommuneInvitationsSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    communeId: z.ZodString;
    userId: z.ZodString;
    status: z.ZodEnum<["pending", "accepted", "rejected", "expired"]>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    status: "pending" | "accepted" | "rejected" | "expired";
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    communeId: string;
}, {
    status: "pending" | "accepted" | "rejected" | "expired";
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    communeId: string;
}>, "many">;
type CreateCommuneInvitationRequest = Infer<typeof CreateCommuneInvitationRequestSchema>;
declare const CreateCommuneInvitationRequestSchema: z.ZodObject<{
    communeId: z.ZodString;
    userId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    userId: string;
    communeId: string;
}, {
    userId: string;
    communeId: string;
}>;
type CommuneJoinRequestStatus = Infer<typeof CommuneJoinRequestStatusSchema>;
declare const CommuneJoinRequestStatusSchema: z.ZodEnum<["pending", "accepted", "rejected"]>;
type CommuneJoinRequest = Infer<typeof CommuneJoinRequestSchema>;
declare const CommuneJoinRequestSchema: z.ZodObject<{
    id: z.ZodString;
    communeId: z.ZodString;
    userId: z.ZodString;
    status: z.ZodEnum<["pending", "accepted", "rejected"]>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    status: "pending" | "accepted" | "rejected";
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    communeId: string;
}, {
    status: "pending" | "accepted" | "rejected";
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    communeId: string;
}>;
type CommuneJoinRequests = Infer<typeof CommuneJoinRequestsSchema>;
declare const CommuneJoinRequestsSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    communeId: z.ZodString;
    userId: z.ZodString;
    status: z.ZodEnum<["pending", "accepted", "rejected"]>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    status: "pending" | "accepted" | "rejected";
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    communeId: string;
}, {
    status: "pending" | "accepted" | "rejected";
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    communeId: string;
}>, "many">;
type CreateCommuneJoinRequestRequest = Infer<typeof CreateCommuneJoinRequestRequestSchema>;
declare const CreateCommuneJoinRequestRequestSchema: z.ZodObject<{
    communeId: z.ZodString;
    userId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    userId: string;
    communeId: string;
}, {
    userId: string;
    communeId: string;
}>;
type TransferHeadStatusRequest = Infer<typeof TransferHeadStatusRequestSchema>;
declare const TransferHeadStatusRequestSchema: z.ZodObject<{
    newHeadUserId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    newHeadUserId: string;
}, {
    newHeadUserId: string;
}>;

type commune_Commune = Commune;
type commune_CommuneInvitation = CommuneInvitation;
declare const commune_CommuneInvitationSchema: typeof CommuneInvitationSchema;
type commune_CommuneInvitationStatus = CommuneInvitationStatus;
declare const commune_CommuneInvitationStatusSchema: typeof CommuneInvitationStatusSchema;
type commune_CommuneInvitations = CommuneInvitations;
declare const commune_CommuneInvitationsSchema: typeof CommuneInvitationsSchema;
type commune_CommuneJoinRequest = CommuneJoinRequest;
declare const commune_CommuneJoinRequestSchema: typeof CommuneJoinRequestSchema;
type commune_CommuneJoinRequestStatus = CommuneJoinRequestStatus;
declare const commune_CommuneJoinRequestStatusSchema: typeof CommuneJoinRequestStatusSchema;
type commune_CommuneJoinRequests = CommuneJoinRequests;
declare const commune_CommuneJoinRequestsSchema: typeof CommuneJoinRequestsSchema;
type commune_CommuneMember = CommuneMember;
declare const commune_CommuneMemberSchema: typeof CommuneMemberSchema;
type commune_CommuneMemberType = CommuneMemberType;
declare const commune_CommuneMemberTypeSchema: typeof CommuneMemberTypeSchema;
type commune_CommuneMembers = CommuneMembers;
declare const commune_CommuneMembersSchema: typeof CommuneMembersSchema;
declare const commune_CommuneSchema: typeof CommuneSchema;
type commune_Communes = Communes;
declare const commune_CommunesSchema: typeof CommunesSchema;
type commune_CreateCommuneInvitationRequest = CreateCommuneInvitationRequest;
declare const commune_CreateCommuneInvitationRequestSchema: typeof CreateCommuneInvitationRequestSchema;
type commune_CreateCommuneJoinRequestRequest = CreateCommuneJoinRequestRequest;
declare const commune_CreateCommuneJoinRequestRequestSchema: typeof CreateCommuneJoinRequestRequestSchema;
type commune_CreateCommuneMemberRequest = CreateCommuneMemberRequest;
declare const commune_CreateCommuneMemberRequestSchema: typeof CreateCommuneMemberRequestSchema;
type commune_CreateCommuneRequest = CreateCommuneRequest;
declare const commune_CreateCommuneRequestSchema: typeof CreateCommuneRequestSchema;
type commune_TransferHeadStatusRequest = TransferHeadStatusRequest;
declare const commune_TransferHeadStatusRequestSchema: typeof TransferHeadStatusRequestSchema;
type commune_UpdateCommuneRequest = UpdateCommuneRequest;
declare const commune_UpdateCommuneRequestSchema: typeof UpdateCommuneRequestSchema;
declare const commune_communeDescription: typeof communeDescription;
declare const commune_communeMemberActorType: typeof communeMemberActorType;
declare const commune_communeMemberName: typeof communeMemberName;
declare const commune_communeName: typeof communeName;
declare namespace commune {
  export { type commune_Commune as Commune, type commune_CommuneInvitation as CommuneInvitation, commune_CommuneInvitationSchema as CommuneInvitationSchema, type commune_CommuneInvitationStatus as CommuneInvitationStatus, commune_CommuneInvitationStatusSchema as CommuneInvitationStatusSchema, type commune_CommuneInvitations as CommuneInvitations, commune_CommuneInvitationsSchema as CommuneInvitationsSchema, type commune_CommuneJoinRequest as CommuneJoinRequest, commune_CommuneJoinRequestSchema as CommuneJoinRequestSchema, type commune_CommuneJoinRequestStatus as CommuneJoinRequestStatus, commune_CommuneJoinRequestStatusSchema as CommuneJoinRequestStatusSchema, type commune_CommuneJoinRequests as CommuneJoinRequests, commune_CommuneJoinRequestsSchema as CommuneJoinRequestsSchema, type commune_CommuneMember as CommuneMember, commune_CommuneMemberSchema as CommuneMemberSchema, type commune_CommuneMemberType as CommuneMemberType, commune_CommuneMemberTypeSchema as CommuneMemberTypeSchema, type commune_CommuneMembers as CommuneMembers, commune_CommuneMembersSchema as CommuneMembersSchema, commune_CommuneSchema as CommuneSchema, type commune_Communes as Communes, commune_CommunesSchema as CommunesSchema, type commune_CreateCommuneInvitationRequest as CreateCommuneInvitationRequest, commune_CreateCommuneInvitationRequestSchema as CreateCommuneInvitationRequestSchema, type commune_CreateCommuneJoinRequestRequest as CreateCommuneJoinRequestRequest, commune_CreateCommuneJoinRequestRequestSchema as CreateCommuneJoinRequestRequestSchema, type commune_CreateCommuneMemberRequest as CreateCommuneMemberRequest, commune_CreateCommuneMemberRequestSchema as CreateCommuneMemberRequestSchema, type commune_CreateCommuneRequest as CreateCommuneRequest, commune_CreateCommuneRequestSchema as CreateCommuneRequestSchema, type commune_TransferHeadStatusRequest as TransferHeadStatusRequest, commune_TransferHeadStatusRequestSchema as TransferHeadStatusRequestSchema, type commune_UpdateCommuneRequest as UpdateCommuneRequest, commune_UpdateCommuneRequestSchema as UpdateCommuneRequestSchema, commune_communeDescription as communeDescription, commune_communeMemberActorType as communeMemberActorType, commune_communeMemberName as communeMemberName, commune_communeName as communeName };
}

declare const postUsefulness: z.ZodNumber;
type Author$1 = Infer<typeof AuthorSchema$1>;
declare const AuthorSchema$1: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    avatar: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    avatar: string | null;
}, {
    id: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    avatar: string | null;
}>;
type RatingType = Infer<typeof RatingTypeSchema>;
declare const RatingTypeSchema: z.ZodEnum<["like", "dislike"]>;
type Rating = Infer<typeof RatingSchema>;
declare const RatingSchema: z.ZodObject<{
    likes: z.ZodNumber;
    dislikes: z.ZodNumber;
    status: z.ZodNullable<z.ZodEnum<["like", "dislike"]>>;
}, "strip", z.ZodTypeAny, {
    status: "like" | "dislike" | null;
    likes: number;
    dislikes: number;
}, {
    status: "like" | "dislike" | null;
    likes: number;
    dislikes: number;
}>;
type PostUsefulness = Infer<typeof PostUsefulnessSchema>;
declare const PostUsefulnessSchema: z.ZodObject<{
    value: z.ZodNullable<z.ZodNumber>;
    count: z.ZodNumber;
    totalValue: z.ZodNullable<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    value: number | null;
    count: number;
    totalValue: number | null;
}, {
    value: number | null;
    count: number;
    totalValue: number | null;
}>;
declare const postTitle: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
declare const postBody: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
declare const postTags: z.ZodArray<z.ZodString, "many">;
type Post = Infer<typeof PostSchema>;
declare const PostSchema: z.ZodObject<{
    id: z.ZodString;
    author: z.ZodObject<{
        id: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        avatar: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        avatar: string | null;
    }, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        avatar: string | null;
    }>;
    rating: z.ZodObject<{
        likes: z.ZodNumber;
        dislikes: z.ZodNumber;
        status: z.ZodNullable<z.ZodEnum<["like", "dislike"]>>;
    }, "strip", z.ZodTypeAny, {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }, {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }>;
    usefulness: z.ZodObject<{
        value: z.ZodNullable<z.ZodNumber>;
        count: z.ZodNumber;
        totalValue: z.ZodNullable<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        value: number | null;
        count: number;
        totalValue: number | null;
    }, {
        value: number | null;
        count: number;
        totalValue: number | null;
    }>;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    body: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    tags: z.ZodArray<z.ZodString, "many">;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    author: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        avatar: string | null;
    };
    rating: {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    };
    usefulness: {
        value: number | null;
        count: number;
        totalValue: number | null;
    };
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    body: {
        value: string;
        locale: "en" | "ru";
    }[];
    tags: string[];
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    author: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        avatar: string | null;
    };
    rating: {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    };
    usefulness: {
        value: number | null;
        count: number;
        totalValue: number | null;
    };
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    body: {
        value: string;
        locale: "en" | "ru";
    }[];
    tags: string[];
}>;
type GetPostsRequest = Infer<typeof GetPostsRequestSchema>;
declare const GetPostsRequestSchema: z.ZodObject<{
    paginationSchema: z.ZodObject<{
        page: z.ZodDefault<z.ZodNumber>;
        size: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        page: number;
        size: number;
    }, {
        page?: number | undefined;
        size?: number | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    paginationSchema: {
        page: number;
        size: number;
    };
}, {
    paginationSchema: {
        page?: number | undefined;
        size?: number | undefined;
    };
}>;
type GetPostsResponse = Infer<typeof GetPostsResponseSchema>;
declare const GetPostsResponseSchema: z.ZodObject<{
    items: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        author: z.ZodObject<{
            id: z.ZodString;
            name: z.ZodArray<z.ZodObject<{
                locale: z.ZodEnum<["en", "ru"]>;
                value: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                value: string;
                locale: "en" | "ru";
            }, {
                value: string;
                locale: "en" | "ru";
            }>, "many">;
            avatar: z.ZodNullable<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        }, {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        }>;
        rating: z.ZodObject<{
            likes: z.ZodNumber;
            dislikes: z.ZodNumber;
            status: z.ZodNullable<z.ZodEnum<["like", "dislike"]>>;
        }, "strip", z.ZodTypeAny, {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        }, {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        }>;
        usefulness: z.ZodObject<{
            value: z.ZodNullable<z.ZodNumber>;
            count: z.ZodNumber;
            totalValue: z.ZodNullable<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            value: number | null;
            count: number;
            totalValue: number | null;
        }, {
            value: number | null;
            count: number;
            totalValue: number | null;
        }>;
        title: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        body: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        tags: z.ZodArray<z.ZodString, "many">;
        createdAt: z.ZodDate;
        updatedAt: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        author: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        };
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number | null;
        };
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
        body: {
            value: string;
            locale: "en" | "ru";
        }[];
        tags: string[];
    }, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        author: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        };
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number | null;
        };
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
        body: {
            value: string;
            locale: "en" | "ru";
        }[];
        tags: string[];
    }>, "many">;
    total: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    items: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        author: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        };
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number | null;
        };
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
        body: {
            value: string;
            locale: "en" | "ru";
        }[];
        tags: string[];
    }[];
    total: number;
}, {
    items: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        author: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        };
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number | null;
        };
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
        body: {
            value: string;
            locale: "en" | "ru";
        }[];
        tags: string[];
    }[];
    total: number;
}>;
type CreatePostRequest = Infer<typeof CreatePostRequestSchema>;
declare const CreatePostRequestSchema: z.ZodObject<{
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    body: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    tags: z.ZodArray<z.ZodString, "many">;
}, "strip", z.ZodTypeAny, {
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    body: {
        value: string;
        locale: "en" | "ru";
    }[];
    tags: string[];
}, {
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    body: {
        value: string;
        locale: "en" | "ru";
    }[];
    tags: string[];
}>;
type UpdatePostRequest = Infer<typeof UpdatePostRequestSchema>;
declare const UpdatePostRequestSchema: z.ZodObject<{
    title: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
    body: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
    tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, "strip", z.ZodTypeAny, {
    title?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    body?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    tags?: string[] | undefined;
}, {
    title?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    body?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    tags?: string[] | undefined;
}>;
type DeletePostRequest = Infer<typeof DeletePostRequestSchema>;
declare const DeletePostRequestSchema: z.ZodObject<{
    reason: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    reason: string | null;
}, {
    reason: string | null;
}>;
type UpdatePostRatingRequest = Infer<typeof UpdatePostRatingRequestSchema>;
declare const UpdatePostRatingRequestSchema: z.ZodObject<{
    type: z.ZodEnum<["like", "dislike"]>;
}, "strip", z.ZodTypeAny, {
    type: "like" | "dislike";
}, {
    type: "like" | "dislike";
}>;
type UpdatePostRatingResponse = Infer<typeof UpdatePostRatingResponseSchema>;
declare const UpdatePostRatingResponseSchema: z.ZodObject<{
    likes: z.ZodNumber;
    dislikes: z.ZodNumber;
    status: z.ZodNullable<z.ZodEnum<["like", "dislike"]>>;
}, "strip", z.ZodTypeAny, {
    status: "like" | "dislike" | null;
    likes: number;
    dislikes: number;
}, {
    status: "like" | "dislike" | null;
    likes: number;
    dislikes: number;
}>;
type UpdatePostUsefulnessRequest = Infer<typeof UpdatePostUsefulnessRequestSchema>;
declare const UpdatePostUsefulnessRequestSchema: z.ZodObject<{
    value: z.ZodNullable<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    value: number | null;
}, {
    value: number | null;
}>;
type UpdatePostUsefulnessResponse = Infer<typeof UpdatePostUsefulnessResponseSchema>;
declare const UpdatePostUsefulnessResponseSchema: z.ZodObject<{
    value: z.ZodNullable<z.ZodNumber>;
    count: z.ZodNumber;
    totalValue: z.ZodNullable<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    value: number | null;
    count: number;
    totalValue: number | null;
}, {
    value: number | null;
    count: number;
    totalValue: number | null;
}>;
type CommentEntityType = Infer<typeof CommentEntityTypeSchema>;
declare const CommentEntityTypeSchema: z.ZodEnum<["post", "comment"]>;
declare const commentBody: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
type Comment = Infer<typeof CommentSchema>;
declare const CommentSchema: z.ZodObject<{
    id: z.ZodString;
    path: z.ZodString;
    author: z.ZodNullable<z.ZodObject<{
        id: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        avatar: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        avatar: string | null;
    }, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        avatar: string | null;
    }>>;
    isAnonymous: z.ZodBoolean;
    anonimityReason: z.ZodNullable<z.ZodString>;
    rating: z.ZodObject<{
        likes: z.ZodNumber;
        dislikes: z.ZodNumber;
        status: z.ZodNullable<z.ZodEnum<["like", "dislike"]>>;
    }, "strip", z.ZodTypeAny, {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }, {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }>;
    body: z.ZodNullable<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
    childrenCount: z.ZodNumber;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    deletedAt: z.ZodNullable<z.ZodDate>;
    deleteReason: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    path: string;
    id: string;
    createdAt: Date;
    updatedAt: Date;
    author: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        avatar: string | null;
    } | null;
    rating: {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    };
    body: {
        value: string;
        locale: "en" | "ru";
    }[] | null;
    isAnonymous: boolean;
    anonimityReason: string | null;
    childrenCount: number;
    deletedAt: Date | null;
    deleteReason: string | null;
}, {
    path: string;
    id: string;
    createdAt: Date;
    updatedAt: Date;
    author: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        avatar: string | null;
    } | null;
    rating: {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    };
    body: {
        value: string;
        locale: "en" | "ru";
    }[] | null;
    isAnonymous: boolean;
    anonimityReason: string | null;
    childrenCount: number;
    deletedAt: Date | null;
    deleteReason: string | null;
}>;
type GetCommentsRequest = Infer<typeof GetCommentsRequestSchema>;
declare const GetCommentsRequestSchema: z.ZodObject<{
    entityType: z.ZodEnum<["post", "comment"]>;
    entityId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    entityType: "post" | "comment";
    entityId: string;
}, {
    entityType: "post" | "comment";
    entityId: string;
}>;
type GetCommentsResponse = Infer<typeof GetCommentsResponseSchema>;
declare const GetCommentsResponseSchema: z.ZodObject<{
    items: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        path: z.ZodString;
        author: z.ZodNullable<z.ZodObject<{
            id: z.ZodString;
            name: z.ZodArray<z.ZodObject<{
                locale: z.ZodEnum<["en", "ru"]>;
                value: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                value: string;
                locale: "en" | "ru";
            }, {
                value: string;
                locale: "en" | "ru";
            }>, "many">;
            avatar: z.ZodNullable<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        }, {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        }>>;
        isAnonymous: z.ZodBoolean;
        anonimityReason: z.ZodNullable<z.ZodString>;
        rating: z.ZodObject<{
            likes: z.ZodNumber;
            dislikes: z.ZodNumber;
            status: z.ZodNullable<z.ZodEnum<["like", "dislike"]>>;
        }, "strip", z.ZodTypeAny, {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        }, {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        }>;
        body: z.ZodNullable<z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">>;
        childrenCount: z.ZodNumber;
        createdAt: z.ZodDate;
        updatedAt: z.ZodDate;
        deletedAt: z.ZodNullable<z.ZodDate>;
        deleteReason: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        path: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        author: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        } | null;
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        body: {
            value: string;
            locale: "en" | "ru";
        }[] | null;
        isAnonymous: boolean;
        anonimityReason: string | null;
        childrenCount: number;
        deletedAt: Date | null;
        deleteReason: string | null;
    }, {
        path: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        author: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        } | null;
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        body: {
            value: string;
            locale: "en" | "ru";
        }[] | null;
        isAnonymous: boolean;
        anonimityReason: string | null;
        childrenCount: number;
        deletedAt: Date | null;
        deleteReason: string | null;
    }>, "many">;
    total: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    items: {
        path: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        author: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        } | null;
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        body: {
            value: string;
            locale: "en" | "ru";
        }[] | null;
        isAnonymous: boolean;
        anonimityReason: string | null;
        childrenCount: number;
        deletedAt: Date | null;
        deleteReason: string | null;
    }[];
    total: number;
}, {
    items: {
        path: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        author: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        } | null;
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        body: {
            value: string;
            locale: "en" | "ru";
        }[] | null;
        isAnonymous: boolean;
        anonimityReason: string | null;
        childrenCount: number;
        deletedAt: Date | null;
        deleteReason: string | null;
    }[];
    total: number;
}>;
type CreateCommentRequest = Infer<typeof CreateCommentRequestSchema>;
declare const CreateCommentRequestSchema: z.ZodObject<{
    entityType: z.ZodEnum<["post", "comment"]>;
    entityId: z.ZodString;
    body: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    body: {
        value: string;
        locale: "en" | "ru";
    }[];
    entityType: "post" | "comment";
    entityId: string;
}, {
    body: {
        value: string;
        locale: "en" | "ru";
    }[];
    entityType: "post" | "comment";
    entityId: string;
}>;
type UpdateCommentRequest = Infer<typeof UpdateCommentRequestSchema>;
declare const UpdateCommentRequestSchema: z.ZodObject<{
    body: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    body: {
        value: string;
        locale: "en" | "ru";
    }[];
}, {
    body: {
        value: string;
        locale: "en" | "ru";
    }[];
}>;
type DeleteCommentRequest = Infer<typeof DeleteCommentRequestSchema>;
declare const DeleteCommentRequestSchema: z.ZodObject<{
    reason: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    reason: string | null;
}, {
    reason: string | null;
}>;
type UpdateCommentRatingRequest = Infer<typeof UpdateCommentRatingRequestSchema>;
declare const UpdateCommentRatingRequestSchema: z.ZodObject<{
    type: z.ZodEnum<["like", "dislike"]>;
}, "strip", z.ZodTypeAny, {
    type: "like" | "dislike";
}, {
    type: "like" | "dislike";
}>;
type UpdateCommentRatingResponse = Infer<typeof UpdateCommentRatingResponseSchema>;
declare const UpdateCommentRatingResponseSchema: z.ZodObject<{
    likes: z.ZodNumber;
    dislikes: z.ZodNumber;
    status: z.ZodNullable<z.ZodEnum<["like", "dislike"]>>;
}, "strip", z.ZodTypeAny, {
    status: "like" | "dislike" | null;
    likes: number;
    dislikes: number;
}, {
    status: "like" | "dislike" | null;
    likes: number;
    dislikes: number;
}>;
type AnonimifyCommentRequest = Infer<typeof AnonimifyCommentRequestSchema>;
declare const AnonimifyCommentRequestSchema: z.ZodObject<{
    reason: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    reason: string | null;
}, {
    reason: string | null;
}>;
type CreateLensRequest = Infer<typeof CreateLensRequestSchema>;
declare const CreateLensRequestSchema: z.ZodObject<{
    name: z.ZodString;
    code: z.ZodString;
}, "strip", z.ZodTypeAny, {
    code: string;
    name: string;
}, {
    code: string;
    name: string;
}>;
type UpdateLensRequest = Infer<typeof UpdateLensRequestSchema>;
declare const UpdateLensRequestSchema: z.ZodObject<{
    name: z.ZodOptional<z.ZodString>;
    code: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    code?: string | undefined;
    name?: string | undefined;
}, {
    code?: string | undefined;
    name?: string | undefined;
}>;

type reactor_AnonimifyCommentRequest = AnonimifyCommentRequest;
declare const reactor_AnonimifyCommentRequestSchema: typeof AnonimifyCommentRequestSchema;
type reactor_Comment = Comment;
type reactor_CommentEntityType = CommentEntityType;
declare const reactor_CommentEntityTypeSchema: typeof CommentEntityTypeSchema;
declare const reactor_CommentSchema: typeof CommentSchema;
type reactor_CreateCommentRequest = CreateCommentRequest;
declare const reactor_CreateCommentRequestSchema: typeof CreateCommentRequestSchema;
type reactor_CreateLensRequest = CreateLensRequest;
declare const reactor_CreateLensRequestSchema: typeof CreateLensRequestSchema;
type reactor_CreatePostRequest = CreatePostRequest;
declare const reactor_CreatePostRequestSchema: typeof CreatePostRequestSchema;
type reactor_DeleteCommentRequest = DeleteCommentRequest;
declare const reactor_DeleteCommentRequestSchema: typeof DeleteCommentRequestSchema;
type reactor_DeletePostRequest = DeletePostRequest;
declare const reactor_DeletePostRequestSchema: typeof DeletePostRequestSchema;
type reactor_GetCommentsRequest = GetCommentsRequest;
declare const reactor_GetCommentsRequestSchema: typeof GetCommentsRequestSchema;
type reactor_GetCommentsResponse = GetCommentsResponse;
declare const reactor_GetCommentsResponseSchema: typeof GetCommentsResponseSchema;
type reactor_GetPostsRequest = GetPostsRequest;
declare const reactor_GetPostsRequestSchema: typeof GetPostsRequestSchema;
type reactor_GetPostsResponse = GetPostsResponse;
declare const reactor_GetPostsResponseSchema: typeof GetPostsResponseSchema;
type reactor_Post = Post;
declare const reactor_PostSchema: typeof PostSchema;
type reactor_PostUsefulness = PostUsefulness;
declare const reactor_PostUsefulnessSchema: typeof PostUsefulnessSchema;
type reactor_Rating = Rating;
declare const reactor_RatingSchema: typeof RatingSchema;
type reactor_RatingType = RatingType;
declare const reactor_RatingTypeSchema: typeof RatingTypeSchema;
type reactor_UpdateCommentRatingRequest = UpdateCommentRatingRequest;
declare const reactor_UpdateCommentRatingRequestSchema: typeof UpdateCommentRatingRequestSchema;
type reactor_UpdateCommentRatingResponse = UpdateCommentRatingResponse;
declare const reactor_UpdateCommentRatingResponseSchema: typeof UpdateCommentRatingResponseSchema;
type reactor_UpdateCommentRequest = UpdateCommentRequest;
declare const reactor_UpdateCommentRequestSchema: typeof UpdateCommentRequestSchema;
type reactor_UpdateLensRequest = UpdateLensRequest;
declare const reactor_UpdateLensRequestSchema: typeof UpdateLensRequestSchema;
type reactor_UpdatePostRatingRequest = UpdatePostRatingRequest;
declare const reactor_UpdatePostRatingRequestSchema: typeof UpdatePostRatingRequestSchema;
type reactor_UpdatePostRatingResponse = UpdatePostRatingResponse;
declare const reactor_UpdatePostRatingResponseSchema: typeof UpdatePostRatingResponseSchema;
type reactor_UpdatePostRequest = UpdatePostRequest;
declare const reactor_UpdatePostRequestSchema: typeof UpdatePostRequestSchema;
type reactor_UpdatePostUsefulnessRequest = UpdatePostUsefulnessRequest;
declare const reactor_UpdatePostUsefulnessRequestSchema: typeof UpdatePostUsefulnessRequestSchema;
type reactor_UpdatePostUsefulnessResponse = UpdatePostUsefulnessResponse;
declare const reactor_UpdatePostUsefulnessResponseSchema: typeof UpdatePostUsefulnessResponseSchema;
declare const reactor_commentBody: typeof commentBody;
declare const reactor_postBody: typeof postBody;
declare const reactor_postTags: typeof postTags;
declare const reactor_postTitle: typeof postTitle;
declare const reactor_postUsefulness: typeof postUsefulness;
declare namespace reactor {
  export { type reactor_AnonimifyCommentRequest as AnonimifyCommentRequest, reactor_AnonimifyCommentRequestSchema as AnonimifyCommentRequestSchema, type Author$1 as Author, AuthorSchema$1 as AuthorSchema, type reactor_Comment as Comment, type reactor_CommentEntityType as CommentEntityType, reactor_CommentEntityTypeSchema as CommentEntityTypeSchema, reactor_CommentSchema as CommentSchema, type reactor_CreateCommentRequest as CreateCommentRequest, reactor_CreateCommentRequestSchema as CreateCommentRequestSchema, type reactor_CreateLensRequest as CreateLensRequest, reactor_CreateLensRequestSchema as CreateLensRequestSchema, type reactor_CreatePostRequest as CreatePostRequest, reactor_CreatePostRequestSchema as CreatePostRequestSchema, type reactor_DeleteCommentRequest as DeleteCommentRequest, reactor_DeleteCommentRequestSchema as DeleteCommentRequestSchema, type reactor_DeletePostRequest as DeletePostRequest, reactor_DeletePostRequestSchema as DeletePostRequestSchema, type reactor_GetCommentsRequest as GetCommentsRequest, reactor_GetCommentsRequestSchema as GetCommentsRequestSchema, type reactor_GetCommentsResponse as GetCommentsResponse, reactor_GetCommentsResponseSchema as GetCommentsResponseSchema, type reactor_GetPostsRequest as GetPostsRequest, reactor_GetPostsRequestSchema as GetPostsRequestSchema, type reactor_GetPostsResponse as GetPostsResponse, reactor_GetPostsResponseSchema as GetPostsResponseSchema, type reactor_Post as Post, reactor_PostSchema as PostSchema, type reactor_PostUsefulness as PostUsefulness, reactor_PostUsefulnessSchema as PostUsefulnessSchema, type reactor_Rating as Rating, reactor_RatingSchema as RatingSchema, type reactor_RatingType as RatingType, reactor_RatingTypeSchema as RatingTypeSchema, type reactor_UpdateCommentRatingRequest as UpdateCommentRatingRequest, reactor_UpdateCommentRatingRequestSchema as UpdateCommentRatingRequestSchema, type reactor_UpdateCommentRatingResponse as UpdateCommentRatingResponse, reactor_UpdateCommentRatingResponseSchema as UpdateCommentRatingResponseSchema, type reactor_UpdateCommentRequest as UpdateCommentRequest, reactor_UpdateCommentRequestSchema as UpdateCommentRequestSchema, type reactor_UpdateLensRequest as UpdateLensRequest, reactor_UpdateLensRequestSchema as UpdateLensRequestSchema, type reactor_UpdatePostRatingRequest as UpdatePostRatingRequest, reactor_UpdatePostRatingRequestSchema as UpdatePostRatingRequestSchema, type reactor_UpdatePostRatingResponse as UpdatePostRatingResponse, reactor_UpdatePostRatingResponseSchema as UpdatePostRatingResponseSchema, type reactor_UpdatePostRequest as UpdatePostRequest, reactor_UpdatePostRequestSchema as UpdatePostRequestSchema, type reactor_UpdatePostUsefulnessRequest as UpdatePostUsefulnessRequest, reactor_UpdatePostUsefulnessRequestSchema as UpdatePostUsefulnessRequestSchema, type reactor_UpdatePostUsefulnessResponse as UpdatePostUsefulnessResponse, reactor_UpdatePostUsefulnessResponseSchema as UpdatePostUsefulnessResponseSchema, reactor_commentBody as commentBody, reactor_postBody as postBody, reactor_postTags as postTags, reactor_postTitle as postTitle, reactor_postUsefulness as postUsefulness };
}

declare const userName: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
declare const userDescription: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
declare const userTitleIsActive: z.ZodBoolean;
declare const userTitleColor: z.ZodNullable<z.ZodString>;
declare const userNoteText: z.ZodString;
type UserRole = Infer<typeof UserRoleSchema>;
declare const UserRoleSchema: z.ZodEnum<["admin", "moderator", "user"]>;
type Author = Infer<typeof AuthorSchema>;
declare const AuthorSchema: z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    images: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }, {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    id: string;
    email: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    images: {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }[];
}, {
    id: string;
    email: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    images: {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }[];
}>;
type User = Infer<typeof UserSchema>;
declare const UserSchema: z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    role: z.ZodEnum<["admin", "moderator", "user"]>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    images: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }, {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }>, "many">;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    email: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    images: {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }[];
    role: "admin" | "moderator" | "user";
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    email: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    images: {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }[];
    role: "admin" | "moderator" | "user";
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
}>;
type Users = Infer<typeof UsersSchema>;
declare const UsersSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    role: z.ZodEnum<["admin", "moderator", "user"]>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    images: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }, {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }>, "many">;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    email: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    images: {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }[];
    role: "admin" | "moderator" | "user";
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    email: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    images: {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }[];
    role: "admin" | "moderator" | "user";
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
}>, "many">;
type UpdateUserRequest = Infer<typeof UpdateUserRequestSchema>;
declare const UpdateUserRequestSchema: z.ZodObject<{
    name: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
    description: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    name?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    description?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
}, {
    name?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    description?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
}>;
type UserTitle = Infer<typeof UserTitleSchema>;
declare const UserTitleSchema: z.ZodObject<{
    id: z.ZodString;
    ownerId: z.ZodNullable<z.ZodString>;
    isActive: z.ZodBoolean;
    color: z.ZodNullable<z.ZodString>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    ownerId: string | null;
    isActive: boolean;
    color: string | null;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    ownerId: string | null;
    isActive: boolean;
    color: string | null;
}>;
type UserTitles = Infer<typeof UserTitlesSchema>;
declare const UserTitlesSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    ownerId: z.ZodNullable<z.ZodString>;
    isActive: z.ZodBoolean;
    color: z.ZodNullable<z.ZodString>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    ownerId: string | null;
    isActive: boolean;
    color: string | null;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    ownerId: string | null;
    isActive: boolean;
    color: string | null;
}>, "many">;
type UpdateUserTitleRequest = Infer<typeof UpdateUserTitleRequestSchema>;
declare const UpdateUserTitleRequestSchema: z.ZodObject<{
    isActive: z.ZodOptional<z.ZodBoolean>;
    color: z.ZodOptional<z.ZodNullable<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    isActive?: boolean | undefined;
    color?: string | null | undefined;
}, {
    isActive?: boolean | undefined;
    color?: string | null | undefined;
}>;
type GetUserNoteResponse = Infer<typeof GetUserNoteResponseSchema>;
declare const GetUserNoteResponseSchema: z.ZodObject<{
    text: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    text: string | null;
}, {
    text: string | null;
}>;
type SetUserNoteRequest = Infer<typeof SetUserNoteRequestSchema>;
declare const SetUserNoteRequestSchema: z.ZodObject<{
    text: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    text: string | null;
}, {
    text: string | null;
}>;

type user_Author = Author;
declare const user_AuthorSchema: typeof AuthorSchema;
type user_GetUserNoteResponse = GetUserNoteResponse;
declare const user_GetUserNoteResponseSchema: typeof GetUserNoteResponseSchema;
type user_SetUserNoteRequest = SetUserNoteRequest;
declare const user_SetUserNoteRequestSchema: typeof SetUserNoteRequestSchema;
type user_UpdateUserRequest = UpdateUserRequest;
declare const user_UpdateUserRequestSchema: typeof UpdateUserRequestSchema;
type user_UpdateUserTitleRequest = UpdateUserTitleRequest;
declare const user_UpdateUserTitleRequestSchema: typeof UpdateUserTitleRequestSchema;
type user_User = User;
type user_UserRole = UserRole;
declare const user_UserRoleSchema: typeof UserRoleSchema;
declare const user_UserSchema: typeof UserSchema;
type user_UserTitle = UserTitle;
declare const user_UserTitleSchema: typeof UserTitleSchema;
type user_UserTitles = UserTitles;
declare const user_UserTitlesSchema: typeof UserTitlesSchema;
type user_Users = Users;
declare const user_UsersSchema: typeof UsersSchema;
declare const user_userDescription: typeof userDescription;
declare const user_userName: typeof userName;
declare const user_userNoteText: typeof userNoteText;
declare const user_userTitleColor: typeof userTitleColor;
declare const user_userTitleIsActive: typeof userTitleIsActive;
declare namespace user {
  export { type user_Author as Author, user_AuthorSchema as AuthorSchema, type user_GetUserNoteResponse as GetUserNoteResponse, user_GetUserNoteResponseSchema as GetUserNoteResponseSchema, type user_SetUserNoteRequest as SetUserNoteRequest, user_SetUserNoteRequestSchema as SetUserNoteRequestSchema, type user_UpdateUserRequest as UpdateUserRequest, user_UpdateUserRequestSchema as UpdateUserRequestSchema, type user_UpdateUserTitleRequest as UpdateUserTitleRequest, user_UpdateUserTitleRequestSchema as UpdateUserTitleRequestSchema, type user_User as User, type user_UserRole as UserRole, user_UserRoleSchema as UserRoleSchema, user_UserSchema as UserSchema, type user_UserTitle as UserTitle, user_UserTitleSchema as UserTitleSchema, type user_UserTitles as UserTitles, user_UserTitlesSchema as UserTitlesSchema, type user_Users as Users, user_UsersSchema as UsersSchema, user_userDescription as userDescription, user_userName as userName, user_userNoteText as userNoteText, user_userTitleColor as userTitleColor, user_userTitleIsActive as userTitleIsActive };
}

declare const karmaPointQuantity: z.ZodNumber;
declare const karmaPointComment: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
type GetKarmaPointsResponse = Infer<typeof GetKarmaPointsResponseSchema>;
declare const GetKarmaPointsResponseSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    author: z.ZodObject<{
        id: z.ZodString;
        email: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        images: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            url: z.ZodString;
            createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
            updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        }, "strip", z.ZodTypeAny, {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }, {
            id: string;
            url: string;
            createdAt: string | number | Date;
            updatedAt: string | number | Date;
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
    }, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: string | number | Date;
            updatedAt: string | number | Date;
        }[];
    }>;
    quantity: z.ZodNumber;
    comment: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    id: string;
    author: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
    };
    comment: {
        value: string;
        locale: "en" | "ru";
    }[];
    quantity: number;
}, {
    id: string;
    author: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: string | number | Date;
            updatedAt: string | number | Date;
        }[];
    };
    comment: {
        value: string;
        locale: "en" | "ru";
    }[];
    quantity: number;
}>, "many">;
type SpendKarmaPointRequest = Infer<typeof SpendKarmaPointRequestSchema>;
declare const SpendKarmaPointRequestSchema: z.ZodObject<{
    sourceUserId: z.ZodString;
    targetUserId: z.ZodString;
    quantity: z.ZodNumber;
    comment: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    comment: {
        value: string;
        locale: "en" | "ru";
    }[];
    quantity: number;
    sourceUserId: string;
    targetUserId: string;
}, {
    comment: {
        value: string;
        locale: "en" | "ru";
    }[];
    quantity: number;
    sourceUserId: string;
    targetUserId: string;
}>;
declare const userFeedbackValue: z.ZodNumber;
declare const userFeedbackText: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
type GetUserFeedbacksResponse = Infer<typeof GetUserFeedbacksResponseSchema>;
declare const GetUserFeedbacksResponseSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    author: z.ZodNullable<z.ZodObject<{
        id: z.ZodString;
        email: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        images: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            url: z.ZodString;
            createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
            updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        }, "strip", z.ZodTypeAny, {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }, {
            id: string;
            url: string;
            createdAt: string | number | Date;
            updatedAt: string | number | Date;
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
    }, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: string | number | Date;
            updatedAt: string | number | Date;
        }[];
    }>>;
    isAnonymous: z.ZodBoolean;
    value: z.ZodNumber;
    text: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    value: number;
    id: string;
    text: {
        value: string;
        locale: "en" | "ru";
    }[];
    author: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
    } | null;
    isAnonymous: boolean;
}, {
    value: number;
    id: string;
    text: {
        value: string;
        locale: "en" | "ru";
    }[];
    author: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        images: {
            id: string;
            url: string;
            createdAt: string | number | Date;
            updatedAt: string | number | Date;
        }[];
    } | null;
    isAnonymous: boolean;
}>, "many">;
type CreateUserFeedbackRequest = Infer<typeof CreateUserFeedbackRequestSchema>;
declare const CreateUserFeedbackRequestSchema: z.ZodObject<{
    sourceUserId: z.ZodString;
    targetUserId: z.ZodString;
    value: z.ZodNumber;
    isAnonymous: z.ZodBoolean;
    text: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    value: number;
    text: {
        value: string;
        locale: "en" | "ru";
    }[];
    isAnonymous: boolean;
    sourceUserId: string;
    targetUserId: string;
}, {
    value: number;
    text: {
        value: string;
        locale: "en" | "ru";
    }[];
    isAnonymous: boolean;
    sourceUserId: string;
    targetUserId: string;
}>;
type GetUserSummaryResponse = Infer<typeof GetUserSummaryResponseSchema>;
declare const GetUserSummaryResponseSchema: z.ZodObject<{
    rating: z.ZodNumber;
    karma: z.ZodNumber;
    rate: z.ZodNullable<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    rating: number;
    karma: number;
    rate: number | null;
}, {
    rating: number;
    karma: number;
    rate: number | null;
}>;

type rating_CreateUserFeedbackRequest = CreateUserFeedbackRequest;
declare const rating_CreateUserFeedbackRequestSchema: typeof CreateUserFeedbackRequestSchema;
type rating_GetKarmaPointsResponse = GetKarmaPointsResponse;
declare const rating_GetKarmaPointsResponseSchema: typeof GetKarmaPointsResponseSchema;
type rating_GetUserFeedbacksResponse = GetUserFeedbacksResponse;
declare const rating_GetUserFeedbacksResponseSchema: typeof GetUserFeedbacksResponseSchema;
type rating_GetUserSummaryResponse = GetUserSummaryResponse;
declare const rating_GetUserSummaryResponseSchema: typeof GetUserSummaryResponseSchema;
type rating_SpendKarmaPointRequest = SpendKarmaPointRequest;
declare const rating_SpendKarmaPointRequestSchema: typeof SpendKarmaPointRequestSchema;
declare const rating_karmaPointComment: typeof karmaPointComment;
declare const rating_karmaPointQuantity: typeof karmaPointQuantity;
declare const rating_userFeedbackText: typeof userFeedbackText;
declare const rating_userFeedbackValue: typeof userFeedbackValue;
declare namespace rating {
  export { type rating_CreateUserFeedbackRequest as CreateUserFeedbackRequest, rating_CreateUserFeedbackRequestSchema as CreateUserFeedbackRequestSchema, type rating_GetKarmaPointsResponse as GetKarmaPointsResponse, rating_GetKarmaPointsResponseSchema as GetKarmaPointsResponseSchema, type rating_GetUserFeedbacksResponse as GetUserFeedbacksResponse, rating_GetUserFeedbacksResponseSchema as GetUserFeedbacksResponseSchema, type rating_GetUserSummaryResponse as GetUserSummaryResponse, rating_GetUserSummaryResponseSchema as GetUserSummaryResponseSchema, type rating_SpendKarmaPointRequest as SpendKarmaPointRequest, rating_SpendKarmaPointRequestSchema as SpendKarmaPointRequestSchema, rating_karmaPointComment as karmaPointComment, rating_karmaPointQuantity as karmaPointQuantity, rating_userFeedbackText as userFeedbackText, rating_userFeedbackValue as userFeedbackValue };
}

export { auth as Auth, common as Common, commune as Commune, rating as Rating, reactor as Reactor, types_d as Types, user as User };
