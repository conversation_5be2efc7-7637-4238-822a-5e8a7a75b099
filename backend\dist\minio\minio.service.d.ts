import { ConfigService } from "@nestjs/config";
export interface FileInfo {
    originalname: string;
    buffer: Buffer;
    mimetype: string;
    size: number;
}
export declare class MinioService {
    private readonly configService;
    private readonly client;
    private readonly logger;
    private readonly buckets;
    constructor(configService: ConfigService);
    private initializeBuckets;
    uploadFile(file: FileInfo, bucket: string, objectName: string): Promise<string>;
    uploadCommuneImage(file: FileInfo, communeId: string, index: number): Promise<string>;
    uploadUserImage(file: FileInfo, userId: string, index: number): Promise<string>;
    deleteFile(bucket: string, objectName: string): Promise<void>;
    deleteCommuneImage(communeId: string, filename: string): Promise<void>;
    deleteUserImage(userId: string, filename: string): Promise<void>;
    uploadPostImage(file: FileInfo, postId: string, index: number): Promise<string>;
    deletePostImage(postId: string, filename: string): Promise<void>;
}
