import { ConfigService } from "@nestjs/config";
import { PrismaService } from "src/prisma/prisma.service";
type CheckEmailOtpDto = {
    email: string;
    otp: string;
};
type CreateEmailOtpDto = {
    email: string;
    otp: string;
    ipAddress: string | null;
    userAgent: string | null;
};
type DeleteEmailOtpDto = {
    id: string;
};
export declare class EmailOtpService {
    private readonly configService;
    private readonly prisma;
    constructor(configService: ConfigService, prisma: PrismaService);
    create(createUserOtpDto: CreateEmailOtpDto): Promise<{
        id: string;
        email: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        expiresAt: Date;
        otp: string;
        ipAddress: string | null;
        userAgent: string | null;
    }>;
    check(checkUserOtpDto: CheckEmailOtpDto): Promise<{
        id: string;
        email: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        expiresAt: Date;
        otp: string;
        ipAddress: string | null;
        userAgent: string | null;
    }>;
    softDelete(deleteUserOtpDto: DeleteEmailOtpDto): Promise<void>;
    delete(deleteUserOtpDto: DeleteEmailOtpDto): Promise<void>;
}
export {};
