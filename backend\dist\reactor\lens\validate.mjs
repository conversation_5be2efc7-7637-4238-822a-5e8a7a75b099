import { z } from "zod";
const operators = {
    equals: z.literal("="),
    notEquals: z.literal("!="),
    greaterThan: z.literal(">"),
    greaterThanOrEqual: z.literal(">="),
    lessThan: z.literal("<"),
    lessThanOrEqual: z.literal("<="),
    like: z.literal("~"),
};
const Hub = z.object({
    identifier: z.literal("hub"),
    operator: z.union([operators.equals, operators.notEquals]),
    value: z.array(z.string().nanoid()),
});
const Group = z.object({
    identifier: z.literal("group"),
    operator: z.union([operators.equals, operators.notEquals]),
    value: z.array(z.string().nanoid()),
});
const Author = z.object({
    identifier: z.literal("author"),
    operator: z.union([operators.equals, operators.notEquals]),
    value: z.array(z.string().nanoid()),
});
const Rating = z.object({
    identifier: z.literal("rating"),
    operator: z.union([
        operators.equals,
        operators.notEquals,
        operators.greaterThan,
        operators.greaterThanOrEqual,
        operators.lessThan,
        operators.lessThanOrEqual,
    ]),
    value: z.coerce.number().int(),
});
const Usefulness = z.object({
    identifier: z.literal("usefulness"),
    operator: z.union([
        operators.equals,
        operators.notEquals,
        operators.greaterThan,
        operators.greaterThanOrEqual,
        operators.lessThan,
        operators.lessThanOrEqual,
    ]),
    value: z.coerce.number().int().min(0).max(10),
});
const Tag = z.object({
    identifier: z.literal("tag"),
    operator: z.union([operators.equals, operators.notEquals]),
    value: z.array(z.string().nanoid()),
});
const Difficulty = z.object({
    identifier: z.literal("difficulty"),
    operator: z.union([operators.equals, operators.notEquals]),
    value: z.array(z.union([z.literal("easy"), z.literal("medium"), z.literal("hard")])),
});
const Title = z.object({
    identifier: z.literal("title"),
    operator: operators.like,
    value: z
        .string()
        .nonempty()
        .max(2 ** 10),
});
const Body = z.object({
    identifier: z.literal("body"),
    operator: operators.like,
    value: z
        .string()
        .nonempty()
        .max(2 ** 10),
});
const Duration = z.object({
    identifier: z.literal("duration"),
    operator: z.union([
        operators.greaterThan,
        operators.greaterThanOrEqual,
        operators.lessThan,
        operators.lessThanOrEqual,
    ]),
    value: z.coerce.number().int().min(0),
});
const MINUTE = 60 * 1000;
const HOUR = 60 * MINUTE;
const DAY = 24 * HOUR;
const MONTH = 30 * DAY;
const YEAR = 365 * DAY;
const Age = z.object({
    identifier: z.literal("age"),
    operator: z.union([
        operators.greaterThan,
        operators.greaterThanOrEqual,
        operators.lessThan,
        operators.lessThanOrEqual,
    ]),
    value: z
        .string()
        .regex(/^\d+(mo|[ywdhm])$/)
        .transform((v) => {
        const match = v.match(/^(?<amount>\d+)(?<unit>mo|[ywdhm])$/);
        if (!match) {
            throw new Error("Invalid duration format");
        }
        const groups = match.groups;
        const amount = parseInt(groups.amount);
        switch (groups.unit) {
            case "y":
                return amount * YEAR;
            case "mo":
                return amount * MONTH;
            case "w":
                return amount * 7 * DAY;
            case "d":
                return amount * DAY;
            case "h":
                return amount * HOUR;
            case "m":
                return amount * MINUTE;
        }
    }),
});
const ComparisonSchema = z.intersection(z.object({
    type: z.literal("comparison"),
}), z.union([
    Hub,
    Group,
    Author,
    Rating,
    Usefulness,
    Tag,
    Difficulty,
    Title,
    Body,
    Duration,
    Age,
]));
export function validate(statement) {
    switch (statement.type) {
        case "and":
        case "or":
            return {
                ...statement,
                statements: statement.statements.map(validate),
            };
        case "comparison": {
            const result = ComparisonSchema.safeParse(statement);
            if (!result.success) {
                throw result.error;
            }
            return result.data;
        }
    }
}
//# sourceMappingURL=validate.mjs.map