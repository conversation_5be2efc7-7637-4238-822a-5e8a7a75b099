"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ReactorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactorService = void 0;
const common_1 = require("@nestjs/common");
const utils_1 = require("../utils");
const prisma_service_1 = require("../prisma/prisma.service");
const errors_1 = require("../common/errors");
const lens_service_1 = require("./lens/lens.service");
let ReactorService = ReactorService_1 = class ReactorService {
    constructor(prisma, lensService) {
        this.prisma = prisma;
        this.lensService = lensService;
        this.logger = new common_1.Logger(ReactorService_1.name);
    }
    async getPosts(pagination, user) {
        const { skip, take } = (0, utils_1.toPrismaPagination)(pagination);
        const total = await this.prisma.reactorPost.count({
            where: {
                deletedAt: null,
            },
        });
        const posts = await this.prisma.reactorPost.findMany({
            where: {
                deletedAt: null,
            },
            include: {
                author: {
                    include: {
                        name: true,
                        images: {
                            orderBy: {
                                createdAt: "desc",
                            },
                            take: 1,
                        },
                    },
                },
                title: true,
                body: true,
                tags: true,
            },
            orderBy: {
                createdAt: "desc",
            },
            skip,
            take,
        });
        const ratingAggregations = await this.prisma.reactorRating.groupBy({
            by: ["entityId", "type"],
            where: {
                entityType: "post",
                entityId: {
                    in: posts.map((post) => post.id),
                },
            },
            _count: {
                type: true,
            },
        });
        const userRatings = await this.prisma.reactorRating.findMany({
            where: {
                userId: user.id,
                entityType: "post",
                entityId: {
                    in: posts.map((post) => post.id),
                },
            },
        });
        const usefulnessAggregations = await this.prisma.reactorUsefulness.groupBy({
            by: ["entityId"],
            where: {
                entityType: "post",
                entityId: {
                    in: posts.map((post) => post.id),
                },
            },
            _sum: {
                value: true,
            },
            _count: {
                value: true,
            },
        });
        const userUsefulness = await this.prisma.reactorUsefulness.findMany({
            where: {
                userId: user.id,
                entityType: "post",
                entityId: {
                    in: posts.map((post) => post.id),
                },
            },
        });
        const transformedPosts = posts.map((post) => {
            const postRatings = ratingAggregations.filter((r) => r.entityId === post.id);
            const likes = postRatings.find((r) => r.type === "like")?._count.type || 0;
            const dislikes = postRatings.find((r) => r.type === "dislike")?._count.type || 0;
            const userRating = userRatings.find((r) => r.entityId === post.id);
            const postUsefulness = usefulnessAggregations.find((u) => u.entityId === post.id);
            const userUsefulnessRating = userUsefulness.find((u) => u.entityId === post.id);
            return {
                id: post.id,
                author: {
                    id: post.author.id,
                    name: post.author.name,
                    avatar: post.author.images[0]?.url ?? null,
                },
                rating: {
                    likes,
                    dislikes,
                    status: userRating?.type ?? null,
                },
                usefulness: {
                    value: userUsefulnessRating?.value ?? null,
                    totalValue: postUsefulness?._sum.value ?? 0,
                    count: postUsefulness?._count.value ?? 0,
                },
                title: post.title,
                body: post.body,
                tags: post.tags.map((t) => t.id),
                createdAt: post.createdAt,
                updatedAt: post.updatedAt,
            };
        });
        return {
            items: transformedPosts,
            total,
        };
    }
    async getPost(id, user) {
        const post = await this.prisma.reactorPost.findUnique({
            where: {
                id,
                deletedAt: null,
            },
            include: {
                author: {
                    include: {
                        name: true,
                        images: {
                            orderBy: {
                                createdAt: "desc",
                            },
                            take: 1,
                        },
                    },
                },
                title: true,
                body: true,
                tags: true,
            },
        });
        if (!post) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("post_not_found"));
        }
        const [likes, dislikes, userRating, usefulness, userUsefulness] = await Promise.all([
            this.prisma.reactorRating.count({
                where: {
                    entityType: "post",
                    entityId: post.id,
                    type: "like",
                },
            }),
            this.prisma.reactorRating.count({
                where: {
                    entityType: "post",
                    entityId: post.id,
                    type: "dislike",
                },
            }),
            this.prisma.reactorRating.findUnique({
                where: {
                    userId_entityType_entityId: {
                        entityId: post.id,
                        entityType: "post",
                        userId: user.id,
                    },
                },
            }),
            this.prisma.reactorUsefulness.aggregate({
                where: {
                    entityType: "post",
                    entityId: post.id,
                },
                _sum: {
                    value: true,
                },
                _count: {
                    value: true,
                },
            }),
            this.prisma.reactorUsefulness.findUnique({
                where: {
                    userId_entityType_entityId: {
                        entityId: post.id,
                        entityType: "post",
                        userId: user.id,
                    },
                },
            }),
        ]);
        return {
            id: post.id,
            author: {
                id: post.author.id,
                name: post.author.name,
                avatar: post.author.images[0]?.url ?? null,
            },
            rating: {
                likes,
                dislikes,
                status: userRating?.type ?? null,
            },
            usefulness: {
                value: userUsefulness?.value ?? null,
                count: usefulness._count.value ?? 0,
                totalValue: usefulness._sum.value ?? 0,
            },
            title: post.title,
            body: post.body,
            tags: post.tags.map((t) => t.id),
            createdAt: post.createdAt,
            updatedAt: post.updatedAt,
        };
    }
    async createPost(dto, user) {
        const post = await this.prisma.reactorPost.create({
            data: {
                authorId: user.id,
                title: {
                    create: dto.title.map((t) => ({
                        key: "title",
                        value: t.value,
                        locale: t.locale,
                    })),
                },
                body: {
                    create: dto.body.map((b) => ({
                        key: "body",
                        value: b.value,
                        locale: b.locale,
                    })),
                },
                tags: {
                    connect: dto.tags.map((tag) => ({ id: tag })),
                },
            },
        });
        return { id: post.id };
    }
    async updatePost(id, dto, user) {
        const post = await this.prisma.reactorPost.findUnique({
            where: { id },
        });
        if (!post) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("post_not_found"));
        }
        if (user.role !== "admin" && user.id !== post.authorId) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_author"));
        }
        await this.prisma.$transaction(async (trx) => {
            await trx.reactorPost.update({
                where: { id },
                data: {
                    title: dto.title && {
                        deleteMany: {},
                    },
                    body: dto.body && {
                        deleteMany: {},
                    },
                },
            });
            await trx.reactorPost.update({
                where: { id },
                data: {
                    title: dto.title && {
                        create: dto.title.map((t) => ({
                            key: "title",
                            value: t.value,
                            locale: t.locale,
                        })),
                    },
                    body: dto.body && {
                        create: dto.body.map((b) => ({
                            key: "body",
                            value: b.value,
                            locale: b.locale,
                        })),
                    },
                    tags: dto.tags && {
                        set: dto.tags.map((tag) => ({ id: tag })),
                    },
                },
            });
        });
        return true;
    }
    async updatePostRating(id, dto, user) {
        const post = await this.prisma.reactorPost.findUnique({
            where: { id },
        });
        if (!post) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("post_not_found"));
        }
        const existingRating = await this.prisma.reactorRating.findFirst({
            where: {
                userId: user.id,
                entityType: "post",
                entityId: post.id,
            },
        });
        let newStatus = null;
        if (existingRating) {
            if (existingRating.type === dto.type) {
                await this.prisma.reactorRating.delete({
                    where: { id: existingRating.id },
                });
            }
            else {
                await this.prisma.reactorRating.update({
                    where: { id: existingRating.id },
                    data: {
                        type: dto.type,
                    },
                });
                newStatus = dto.type;
            }
        }
        else {
            await this.prisma.reactorRating.create({
                data: {
                    userId: user.id,
                    entityType: "post",
                    entityId: post.id,
                    type: dto.type,
                },
            });
            newStatus = dto.type;
        }
        const [likes, dislikes] = await Promise.all([
            this.prisma.reactorRating.count({
                where: {
                    entityType: "post",
                    entityId: post.id,
                    type: "like",
                },
            }),
            this.prisma.reactorRating.count({
                where: {
                    entityType: "post",
                    entityId: post.id,
                    type: "dislike",
                },
            }),
        ]);
        return {
            likes,
            dislikes,
            status: newStatus,
        };
    }
    async updatePostUsefulness(id, dto, user) {
        const post = await this.prisma.reactorPost.findUnique({
            where: { id },
        });
        if (!post) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("post_not_found"));
        }
        if (dto.value === null) {
            await this.prisma.reactorUsefulness.delete({
                where: {
                    userId_entityType_entityId: {
                        entityId: post.id,
                        entityType: "post",
                        userId: user.id,
                    },
                },
            });
        }
        else {
            await this.prisma.reactorUsefulness.upsert({
                where: {
                    userId_entityType_entityId: {
                        entityId: post.id,
                        entityType: "post",
                        userId: user.id,
                    },
                },
                create: {
                    entityId: post.id,
                    entityType: "post",
                    userId: user.id,
                    value: dto.value,
                },
                update: {
                    value: dto.value,
                },
            });
        }
        const [count, totalValue] = await Promise.all([
            this.prisma.reactorUsefulness.count({
                where: {
                    entityType: "post",
                    entityId: post.id,
                },
            }),
            this.prisma.reactorUsefulness.aggregate({
                where: {
                    entityType: "post",
                    entityId: post.id,
                },
                _sum: {
                    value: true,
                },
            }),
        ]);
        return {
            count,
            totalValue: totalValue._sum.value,
            value: dto.value,
        };
    }
    async deletePost(id, dto, user) {
        const post = await this.prisma.reactorPost.findUnique({
            where: { id },
        });
        if (!post) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("post_not_found"));
        }
        if (user.role !== "admin" && user.id !== post.authorId) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_author"));
        }
        await this.prisma.reactorPost.update({
            where: { id },
            data: {
                deleteReason: dto.reason,
                deletedAt: new Date(),
            },
        });
        return true;
    }
    async getComments(dto, user) {
        if (dto.entityType === "comment") {
            throw new common_1.BadRequestException(...(0, errors_1.getError)("get_comments_for_comment_not_implemented"));
        }
        const { entityId } = dto;
        const post = await this.prisma.reactorPost.findUnique({
            where: { id: entityId },
        });
        if (!post) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("post_not_found"));
        }
        const rawComments = await this.prisma.$queryRaw `
            SELECT
                comments.id,

                comments.post_id,

                comments.path,
                comments.internal_number,

                COUNT(DISTINCT rating_likes.id)::INT AS rating_likes,
                COUNT(DISTINCT rating_dislikes.id)::INT AS rating_dislikes,
                rating_status.type AS rating_status,

                comments.author_id,
                comments.is_anonymous,
                comments.anonimity_reason,

                COUNT(DISTINCT children.id)::INT - 1 AS children_count,

                comments.delete_reason,

                comments.created_at,
                comments.updated_at,
                comments.deleted_at

            FROM reactor_comments comments

            LEFT JOIN reactor_comments children
                ON children.path <@ comments.path
                AND children.post_id = comments.post_id

            LEFT JOIN reactor_ratings rating_likes
                ON rating_likes.entity_id = comments.id
                AND rating_likes.entity_type = 'comment'
                AND rating_likes.type = 'like'

            LEFT JOIN reactor_ratings rating_dislikes
                ON rating_dislikes.entity_id = comments.id
                AND rating_dislikes.entity_type = 'comment'
                AND rating_dislikes.type = 'dislike'

            LEFT JOIN reactor_ratings rating_status
                ON rating_status.entity_id = comments.id
                AND rating_status.entity_type = 'comment'
                AND rating_status.user_id = ${user.id}

            WHERE comments.post_id = ${entityId}

            GROUP BY
                comments.id,
                rating_status.type

            ORDER BY path ASC
        `;
        const users = await this.prisma.user.findMany({
            where: {
                id: {
                    in: rawComments.map((c) => c.author_id),
                },
            },
            include: {
                name: true,
                images: {
                    orderBy: {
                        createdAt: "desc",
                    },
                    take: 1,
                },
            },
        });
        const userMap = new Map(users.map((u) => [u.id, u]));
        const commentBodies = await this.prisma.reactorComment.findMany({
            where: {
                id: {
                    in: rawComments.map((c) => c.id),
                },
            },
            include: {
                body: true,
            },
        });
        const commentBodyMap = new Map(commentBodies.map((c) => [c.id, c.body]));
        const comments = rawComments.map((c) => {
            const user = userMap.get(c.author_id);
            const body = commentBodyMap.get(c.id);
            return {
                id: c.id,
                path: c.path,
                internalNumber: c.internal_number,
                author: c.is_anonymous
                    ? null
                    : {
                        id: user.id,
                        name: user.name,
                        avatar: user.images[0]?.url ?? null,
                    },
                isAnonymous: c.is_anonymous,
                anonimityReason: c.anonimity_reason,
                rating: {
                    likes: c.rating_likes,
                    dislikes: c.rating_dislikes,
                    status: c.rating_status,
                },
                body: c.deleted_at ? null : body,
                childrenCount: c.children_count,
                deleteReason: c.delete_reason,
                createdAt: c.created_at,
                updatedAt: c.updated_at,
                deletedAt: c.deleted_at,
            };
        });
        return {
            items: comments,
            total: 0,
        };
    }
    async getComment(dto, user) {
        const { id } = dto;
        const rawComment = await this.prisma.$queryRaw `
            SELECT
                comments.id,

                comments.post_id,

                comments.path,
                comments.internal_number,

                COUNT(DISTINCT rating_likes.id)::INT AS rating_likes,
                COUNT(DISTINCT rating_dislikes.id)::INT AS rating_dislikes,
                rating_status.type AS rating_status,

                comments.author_id,
                comments.is_anonymous,
                comments.anonimity_reason,

                COUNT(DISTINCT children.id)::INT - 1 AS children_count,

                comments.delete_reason,

                comments.created_at,
                comments.updated_at,
                comments.deleted_at

            FROM reactor_comments comments

            LEFT JOIN reactor_comments children
                ON children.path <@ comments.path
                AND children.post_id = comments.post_id

            LEFT JOIN reactor_ratings rating_likes
                ON rating_likes.entity_id = comments.id
                AND rating_likes.entity_type = 'comment'
                AND rating_likes.type = 'like'

            LEFT JOIN reactor_ratings rating_dislikes
                ON rating_dislikes.entity_id = comments.id
                AND rating_dislikes.entity_type = 'comment'
                AND rating_dislikes.type = 'dislike'

            LEFT JOIN reactor_ratings rating_status
                ON rating_status.entity_id = comments.id
                AND rating_status.entity_type = 'comment'
                AND rating_status.user_id = ${user.id}

            WHERE comments.id = ${id}

            GROUP BY
                comments.id,
                rating_status.type

            ORDER BY path ASC

            LIMIT 1
        `.then((r) => r[0]);
        if (!rawComment) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("comment_not_found"));
        }
        const author = await this.prisma.user.findUnique({
            where: { id: rawComment.author_id },
            include: {
                name: true,
                images: {
                    orderBy: {
                        createdAt: "desc",
                    },
                    take: 1,
                },
            },
        });
        if (!author) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("user_not_found"));
        }
        const body = await this.prisma.reactorComment
            .findUnique({
            where: {
                id: rawComment.id,
            },
            select: {
                body: true,
            },
        })
            .then((r) => r.body);
        return {
            id: rawComment.id,
            path: rawComment.path,
            internalNumber: rawComment.internal_number,
            author: rawComment.is_anonymous
                ? null
                : {
                    id: author.id,
                    name: author.name,
                    avatar: author.images[0]?.url ?? null,
                },
            isAnonymous: rawComment.is_anonymous,
            anonimityReason: rawComment.anonimity_reason,
            rating: {
                likes: rawComment.rating_likes,
                dislikes: rawComment.rating_dislikes,
                status: rawComment.rating_status,
            },
            body: rawComment.deleted_at ? null : body,
            childrenCount: rawComment.children_count,
            deleteReason: rawComment.delete_reason,
            createdAt: rawComment.created_at,
            updatedAt: rawComment.updated_at,
            deletedAt: rawComment.deleted_at,
        };
    }
    async getNextCommentInternalNumber(postId) {
        const { internalNumber } = await this.prisma.reactorPostInternalNumber.upsert({
            where: { postId },
            update: { internalNumber: { increment: 1 } },
            create: { postId, internalNumber: 1 },
        });
        return internalNumber;
    }
    async createComment(dto, user) {
        const { entityType, entityId, body } = dto;
        if (entityType === "post") {
            const comment = await this.createPostComment({
                postId: entityId,
                body,
            }, user);
            return { id: comment.id };
        }
        if (entityType === "comment") {
            const comment = await this.createCommentComment({
                commentId: entityId,
                body,
            }, user);
            return { id: comment.id };
        }
        throw new Error("Impossible");
    }
    async createPostComment(dto, user) {
        const post = await this.prisma.reactorPost.findUnique({
            where: { id: dto.postId },
        });
        if (!post) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("post_not_found"));
        }
        const internalNumber = await this.getNextCommentInternalNumber(post.id);
        return await this.prisma.reactorComment.create({
            data: {
                postId: post.id,
                internalNumber,
                path: internalNumber.toString(),
                authorId: user.id,
                body: {
                    create: dto.body.map((b) => ({
                        key: "body",
                        value: b.value,
                        locale: b.locale,
                    })),
                },
            },
        });
    }
    async createCommentComment(dto, user) {
        const parentComment = await this.prisma.reactorComment.findUnique({
            where: { id: dto.commentId },
        });
        if (!parentComment) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("comment_not_found"));
        }
        const internalNumber = await this.getNextCommentInternalNumber(parentComment.postId);
        return await this.prisma.reactorComment.create({
            data: {
                postId: parentComment.postId,
                internalNumber,
                path: `${parentComment.path}.${internalNumber}`,
                authorId: user.id,
                body: {
                    create: dto.body.map((b) => ({
                        key: "body",
                        value: b.value,
                        locale: b.locale,
                    })),
                },
            },
        });
    }
    async updateComment(id, dto, user) {
        const comment = await this.prisma.reactorComment.findUnique({
            where: { id },
        });
        if (!comment) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("comment_not_found"));
        }
        if (user.role !== "admin" && user.id !== comment.authorId) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_author"));
        }
        await this.prisma.$transaction(async (trx) => {
            await trx.reactorComment.update({
                where: { id },
                data: {
                    body: dto.body && {
                        deleteMany: {},
                    },
                },
            });
            await trx.reactorComment.update({
                where: { id },
                data: {
                    body: dto.body && {
                        create: dto.body.map((b) => ({
                            key: "body",
                            value: b.value,
                            locale: b.locale,
                        })),
                    },
                },
            });
        });
        return true;
    }
    async updateCommentRating(id, dto, user) {
        const comment = await this.prisma.reactorComment.findUnique({
            where: { id },
        });
        if (!comment) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("comment_not_found"));
        }
        const existingRating = await this.prisma.reactorRating.findFirst({
            where: {
                userId: user.id,
                entityType: "comment",
                entityId: comment.id,
            },
        });
        let newStatus = null;
        if (existingRating) {
            if (existingRating.type === dto.type) {
                await this.prisma.reactorRating.delete({
                    where: { id: existingRating.id },
                });
            }
            else {
                await this.prisma.reactorRating.update({
                    where: { id: existingRating.id },
                    data: {
                        type: dto.type,
                    },
                });
                newStatus = dto.type;
            }
        }
        else {
            await this.prisma.reactorRating.create({
                data: {
                    userId: user.id,
                    entityId: comment.id,
                    entityType: "comment",
                    type: dto.type,
                },
            });
            newStatus = dto.type;
        }
        const [likes, dislikes] = await Promise.all([
            this.prisma.reactorRating.count({
                where: {
                    entityType: "comment",
                    entityId: comment.id,
                    type: "like",
                },
            }),
            this.prisma.reactorRating.count({
                where: {
                    entityType: "comment",
                    entityId: comment.id,
                    type: "dislike",
                },
            }),
        ]);
        return {
            likes,
            dislikes,
            status: newStatus,
        };
    }
    async anonimifyComment(id, dto, user) {
        const comment = await this.prisma.reactorComment.findUnique({
            where: { id },
        });
        if (!comment) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("comment_not_found"));
        }
        if (user.role !== "admin" && user.id !== comment.authorId) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_author"));
        }
        await this.prisma.reactorComment.update({
            where: { id },
            data: {
                isAnonymous: true,
                anonimityReason: dto.reason,
            },
        });
        return true;
    }
    async deleteComment(id, dto, user) {
        const comment = await this.prisma.reactorComment.findUnique({
            where: { id },
        });
        if (!comment) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("comment_not_found"));
        }
        if (user.role !== "admin" && user.id !== comment.authorId) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_author"));
        }
        await this.prisma.reactorComment.update({
            where: { id },
            data: {
                deleteReason: dto.reason,
                deletedAt: new Date(),
            },
        });
        return true;
    }
    async getLenses(user) {
        return this.prisma.reactorLens.findMany({
            where: {
                userId: user.id,
                deletedAt: null,
            },
        });
    }
    generateLensSql(code) {
        try {
            return this.lensService.generateSql(code);
        }
        catch (error) {
            this.logger.error(error);
            throw new common_1.BadRequestException(String(error));
        }
    }
    async createLens(dto, user) {
        const sql = this.generateLensSql(dto.code);
        const lens = await this.prisma.reactorLens.create({
            data: {
                userId: user.id,
                name: dto.name,
                code: dto.code,
                sql,
            },
        });
        return { id: lens.id };
    }
    async updateLens(id, dto, user) {
        const lens = await this.prisma.reactorLens.findUniqueOrThrow({
            where: { id, deletedAt: null },
        });
        if (user.role !== "admin" && lens.userId !== user.id) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_author"));
        }
        const data = Object.assign({}, dto.name ? { name: dto.name } : undefined, dto.code
            ? { code: dto.code, sql: this.generateLensSql(dto.code) }
            : null);
        await this.prisma.reactorLens.update({
            where: { id },
            data,
        });
        return true;
    }
    async deleteLens(id, user) {
        const lens = await this.prisma.reactorLens.findUniqueOrThrow({
            where: { id, deletedAt: null },
        });
        if (user.role !== "admin" && lens.userId !== user.id) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_author"));
        }
        await this.prisma.reactorLens.update({
            where: { id },
            data: {
                deletedAt: new Date(),
            },
        });
    }
};
exports.ReactorService = ReactorService;
exports.ReactorService = ReactorService = ReactorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        lens_service_1.ReactorLensService])
], ReactorService);
//# sourceMappingURL=reactor.service.js.map