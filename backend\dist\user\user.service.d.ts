import { Prisma } from "@prisma/client";
import { BaseService } from "src/common/base-service";
import { PrismaService } from "src/prisma/prisma.service";
import { CurrentUser } from "src/auth/types";
import { MinioService, FileInfo } from "src/minio/minio.service";
import { Common } from "@commune/api";
export type CreateUser = {
    referrerId: string | null;
    email: string;
};
export declare class UserService extends BaseService {
    private readonly prisma;
    private readonly minioService;
    constructor(prisma: PrismaService, minioService: MinioService);
    canGet(id: string, user: CurrentUser): Promise<true>;
    canChange(id: string, user: CurrentUser): Promise<true>;
    getByEmail(email: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        deletedAt: Date | null;
    } | null>;
    create(data: CreateUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        deletedAt: Date | null;
    }>;
    check(ids: string[]): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        deletedAt: Date | null;
    }[]>;
    getOne(id: string): Promise<({
        description: {
            value: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
        }[];
        name: {
            value: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
        }[];
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        titles: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            ownerId: string | null;
            isActive: boolean;
            color: string | null;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        deletedAt: Date | null;
    }) | null>;
    getOneOrThrow(id: string): Promise<{
        description: {
            value: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
        }[];
        name: {
            value: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
        }[];
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        titles: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            ownerId: string | null;
            isActive: boolean;
            color: string | null;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        deletedAt: Date | null;
    }>;
    getMany(where: Prisma.UserWhereInput, pagination?: {
        page: number;
        size: number;
    }): Promise<({
        description: {
            value: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
        }[];
        name: {
            value: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
        }[];
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        titles: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            ownerId: string | null;
            isActive: boolean;
            color: string | null;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        deletedAt: Date | null;
    })[]>;
    createOne(data: Prisma.UserCreateInput): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        deletedAt: Date | null;
    }>;
    createMany(data: Prisma.UserCreateManyInput[]): Promise<Prisma.BatchPayload>;
    updateOne(id: string, data: Prisma.UserUpdateInput): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        deletedAt: Date | null;
    }>;
    update(id: string, data: Partial<{
        name: Common.Localization[];
        description: Common.Localization[];
    }>, user: CurrentUser): Promise<{
        description: {
            value: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
        }[];
        name: {
            value: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
        }[];
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        deletedAt: Date | null;
    }>;
    uploadUserImage(userId: string, file: FileInfo): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        url: string;
    }>;
    updateMany(where: Prisma.UserWhereInput, data: Prisma.UserUpdateInput): Promise<Prisma.BatchPayload>;
    softDeleteOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        deletedAt: Date | null;
    }>;
    softDeleteMany(where: Prisma.UserWhereInput): Promise<Prisma.BatchPayload>;
    deleteOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        deletedAt: Date | null;
    }>;
    deleteMany(where: Prisma.UserWhereInput): Promise<Prisma.BatchPayload>;
    getUserNote(userId: string, currentUser: CurrentUser): Promise<{
        targetUserId: string;
        id: string;
        sourceUserId: string;
        createdAt: Date;
        updatedAt: Date;
        text: string;
    } | null>;
    setUserNote(data: {
        userId: string;
        text: string | null;
    }, currentUser: CurrentUser): Promise<boolean>;
}
