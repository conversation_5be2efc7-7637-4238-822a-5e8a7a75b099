import type { Infer } from "./types";

import { z } from "zod";

export const id = z.string().nanoid();
export const email = z.string().email();

export const stringToDate = z.union([z.number(), z.string(), z.date()]).pipe(z.coerce.date());

export function JsonStringToObject<T extends z.ZodRawShape>(schema: T) {
    return z
        .string()
        .transform((value) => JSON.parse(value))
        .pipe(z.object(schema));
}

export function FormDataToObject<T extends z.ZodRawShape>(schema: T) {
    return z.object({
        data: JsonStringToObject(schema),
    });
}

export type ObjectWithId = Infer<typeof ObjectWithIdSchema>;
export const ObjectWithIdSchema = z.object({ id });

export type WebsiteLocale = Infer<typeof WebsiteLocaleSchema>;
export const WebsiteLocaleSchema = z.enum(["en", "ru"]);

export type LocalizationLocale = Infer<typeof LocalizationLocaleSchema>;
export const LocalizationLocaleSchema = z.enum(["en", "ru"]);

export type LocalizationLocales = Infer<typeof LocalizationLocalesSchema>;
export const LocalizationLocalesSchema = z.array(LocalizationLocaleSchema).min(1);

export type Localization = Infer<typeof LocalizationSchema>;
export const LocalizationSchema = z.object({
    locale: LocalizationLocaleSchema,
    value: z.string().nonempty(),
});

export type Localizations = Infer<typeof LocalizationsSchema>;
export const LocalizationsSchema = z.array(LocalizationSchema);

export type Image = Infer<typeof ImageSchema>;
export const ImageSchema = z.object({
    id,
    url: z.string(),
    createdAt: stringToDate,
    updatedAt: stringToDate,
});

export type Images = Infer<typeof ImagesSchema>;
export const ImagesSchema = z.array(ImageSchema);

export const pagination = {
    offset: z.coerce.number()
        .int()
        .default(0),
    limit: z.coerce.number()
        .int()
        .positive()
        .max(100)
        .default(20),

    page: z.coerce.number()
        .int()
        .positive()
        .default(1),
    size: z.coerce.number()
        .int()
        .positive()
        .max(100)
        .default(20),
};

export type Pagination = Infer<typeof PaginationSchema>;
export const PaginationSchema = z
    .object({
        page: pagination.page,
        size: pagination.size,
    });

export function parseInput<T extends z.ZodTypeAny>(
    schema: T,
    value: z.input<T>,
): z.output<T> {
    return schema.parse(value);
}

export function parseUnknown<T extends z.ZodTypeAny>(
    schema: T,
    value: unknown,
): z.output<T> {
    return schema.parse(value);
}
