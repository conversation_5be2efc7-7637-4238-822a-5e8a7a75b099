{"name": "backend", "version": "1.0.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main.js", "start:deploy": "prisma migrate deploy && npm run start:prod", "dev": "nest start --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "generate": "prisma generate"}, "prisma": {"schema": "./prisma/schema", "seed": "tsx ./prisma/seed/seed.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.758.0", "@faker-js/faker": "^9.8.0", "@nestjs/common": "^11.0.11", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.11", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.11", "@ocelotjungle/collections": "^1.0.1", "@prisma/client": "^6.11.1", "bcrypt": "^5.1.1", "chevrotain": "^11.0.3", "cookie-parser": "^1.4.7", "express-session": "^1.18.1", "minio": "^8.0.5", "multer": "^1.4.5-lts.2", "nestjs-pino": "^4.3.1", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "session-file-store": "^1.5.0", "@commune/api": "file:*"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "devDependencies": {"@nestjs/cli": "^11.0.5", "@nestjs/schematics": "^11.0.2", "@nestjs/testing": "^11.0.11", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/express": "^5.0.0", "@types/express-session": "^1.18.2", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.13.10", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/session-file-store": "^1.2.5", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.7.0", "prettier": "^3.5.3", "prisma": "^6.11.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.6", "ts-loader": "^9.5.2", "ts-morph": "^25.0.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0"}}