import { z } from "zod";
export declare const ALLOWED_FILE_TYPES: string[];
export declare const MAX_FILE_SIZE: number;
export declare const MAX_FILES_COUNT = 10;
export declare const FileUploadSchema: z.ZodObject<{
    mimetype: z.ZodE<PERSON><[string, ...string[]]>;
    size: z.ZodNumber;
    originalname: z.ZodString;
    buffer: z.ZodType<Buffer<ArrayBufferLike>, z.ZodTypeDef, Buffer<ArrayBufferLike>>;
}, "strip", z.ZodTypeAny, {
    size: number;
    mimetype: string;
    originalname: string;
    buffer: Buffer<ArrayBufferLike>;
}, {
    size: number;
    mimetype: string;
    originalname: string;
    buffer: Buffer<ArrayBufferLike>;
}>;
export type FileUpload = z.infer<typeof FileUploadSchema>;
