// post-status
enum PostStatus {
    @@map("post_status")

    draft
    published
    archived
}

// post
model Post {
    @@map("posts")

    id String @id @default(nanoid())

    images Image[] @relation("post_images")
    tags   Tag[]   @relation("post_tags")

    title       Localization[] @relation("post_title")
    description Localization[] @relation("post_description")

    status PostStatus @default(draft)

    publishedAt DateTime? @map("published_at") @db.Timestamptz(3)

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}
