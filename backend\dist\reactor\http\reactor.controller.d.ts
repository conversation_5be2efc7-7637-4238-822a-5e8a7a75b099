import { CurrentUser } from "src/auth/types";
import { ReactorService } from "../reactor.service";
import { Common, Reactor } from "@commune/api";
export declare class ReactorController {
    private readonly reactorService;
    constructor(reactorService: ReactorService);
    getPosts(pagination: Common.Pagination, user: CurrentUser): Promise<{
        items: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            author: {
                id: string;
                name: {
                    value: string;
                    locale: "en" | "ru";
                }[];
                avatar: string | null;
            };
            rating: {
                status: "like" | "dislike" | null;
                likes: number;
                dislikes: number;
            };
            usefulness: {
                value: number | null;
                count: number;
                totalValue: number | null;
            };
            title: {
                value: string;
                locale: "en" | "ru";
            }[];
            body: {
                value: string;
                locale: "en" | "ru";
            }[];
            tags: string[];
        }[];
        total: number;
    }>;
    getPost(id: string, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        author: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        };
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number | null;
        };
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
        body: {
            value: string;
            locale: "en" | "ru";
        }[];
        tags: string[];
    }>;
    createPost(body: Reactor.CreatePostRequest, user: CurrentUser): Promise<{
        id: string;
    }>;
    updatePost(id: string, body: Reactor.UpdatePostRequest, user: CurrentUser): Promise<boolean>;
    updatePostRating(id: string, body: Reactor.UpdatePostRatingRequest, user: CurrentUser): Promise<{
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }>;
    updatePostUsefulness(id: string, body: Reactor.UpdatePostUsefulnessRequest, user: CurrentUser): Promise<{
        value: number | null;
        count: number;
        totalValue: number | null;
    }>;
    deletePost(id: string, body: Reactor.DeletePostRequest, user: CurrentUser): Promise<boolean>;
    getComments(body: Reactor.GetCommentsRequest, user: CurrentUser): Promise<{
        items: {
            path: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            author: {
                id: string;
                name: {
                    value: string;
                    locale: "en" | "ru";
                }[];
                avatar: string | null;
            } | null;
            rating: {
                status: "like" | "dislike" | null;
                likes: number;
                dislikes: number;
            };
            body: {
                value: string;
                locale: "en" | "ru";
            }[] | null;
            isAnonymous: boolean;
            anonimityReason: string | null;
            childrenCount: number;
            deletedAt: Date | null;
            deleteReason: string | null;
        }[];
        total: number;
    }>;
    getComment(id: string, user: CurrentUser): Promise<{
        path: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        author: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        } | null;
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        body: {
            value: string;
            locale: "en" | "ru";
        }[] | null;
        isAnonymous: boolean;
        anonimityReason: string | null;
        childrenCount: number;
        deletedAt: Date | null;
        deleteReason: string | null;
    }>;
    createComment(body: Reactor.CreateCommentRequest, user: CurrentUser): Promise<{
        id: string;
    }>;
    updateComment(id: string, body: Reactor.UpdateCommentRequest, user: CurrentUser): Promise<boolean>;
    updateCommentRating(id: string, body: Reactor.UpdateCommentRatingRequest, user: CurrentUser): Promise<{
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }>;
    anonimifyComment(id: string, body: Reactor.AnonimifyCommentRequest, user: CurrentUser): Promise<boolean>;
    deleteComment(id: string, body: Reactor.DeleteCommentRequest, user: CurrentUser): Promise<boolean>;
    getLenses(user: CurrentUser): Promise<{
        code: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        deletedAt: Date | null;
        userId: string;
        sql: string;
    }[]>;
    createLens(body: Reactor.CreateLensRequest, user: CurrentUser): Promise<{
        id: string;
    }>;
    updateLens(id: string, body: Reactor.UpdateLensRequest, user: CurrentUser): Promise<boolean>;
    deleteLens(id: string, user: CurrentUser): Promise<void>;
}
