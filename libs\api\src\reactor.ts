import type { Infer } from "./types";

import { z } from "zod";
import { id, LocalizationsSchema, PaginationSchema } from "./common";
import { userName } from "./user";

export const postUsefulness = z.number().int().min(0).max(10);

export type Author = Infer<typeof AuthorSchema>;
export const AuthorSchema = z.object({
    id,
    name: userName,
    avatar: z.string().nullable(),
})

export type RatingType = Infer<typeof RatingTypeSchema>;
export const RatingTypeSchema = z.enum(["like", "dislike"]);

export type Rating = Infer<typeof RatingSchema>;
export const RatingSchema = z.object({
    likes: z.number().int().nonnegative(),
    dislikes: z.number().int().nonnegative(),
    status: RatingTypeSchema.nullable(),
});

export type PostUsefulness = Infer<typeof PostUsefulnessSchema>;
export const PostUsefulnessSchema = z.object({
    value: postUsefulness.nullable(),
    count: z.number().int().nonnegative(),
    totalValue: z.number().min(0).max(10).nullable(),
});

export const postTitle = LocalizationsSchema.min(1);
export const postBody = LocalizationsSchema.min(1);
export const postTags = z.array(id);

export type Post = Infer<typeof PostSchema>;
export const PostSchema = z.object({
    id,

    author: AuthorSchema,

    rating: RatingSchema,
    usefulness: PostUsefulnessSchema,

    title: postTitle,
    body: postBody,

    tags: postTags,

    createdAt: z.date(),
    updatedAt: z.date(),
});

export type GetPostsRequest = Infer<typeof GetPostsRequestSchema>;
export const GetPostsRequestSchema = z.object({
    paginationSchema: PaginationSchema,
});

export type GetPostsResponse = Infer<typeof GetPostsResponseSchema>;
export const GetPostsResponseSchema = z.object({
    items: z.array(PostSchema),
    total: z.number().int().nonnegative(),
});

export type CreatePostRequest = Infer<typeof CreatePostRequestSchema>;
export const CreatePostRequestSchema = z.object({
    title: postTitle,
    body: postBody,
    tags: postTags,
});

export type UpdatePostRequest = Infer<typeof UpdatePostRequestSchema>;
export const UpdatePostRequestSchema = z
    .object({
        title: postTitle,
        body: postBody,
        tags: postTags,
    })
    .partial();

export type DeletePostRequest = Infer<typeof DeletePostRequestSchema>;
export const DeletePostRequestSchema = z.object({
    reason: z.string().nonempty().nullable(),
});

export type UpdatePostRatingRequest = Infer<typeof UpdatePostRatingRequestSchema>;
export const UpdatePostRatingRequestSchema = z.object({
    type: RatingTypeSchema,
});

export type UpdatePostRatingResponse = Infer<typeof UpdatePostRatingResponseSchema>;
export const UpdatePostRatingResponseSchema = RatingSchema;

export type UpdatePostUsefulnessRequest = Infer<typeof UpdatePostUsefulnessRequestSchema>;
export const UpdatePostUsefulnessRequestSchema = z.object({
    value: postUsefulness.nullable(),
});

export type UpdatePostUsefulnessResponse = Infer<typeof UpdatePostUsefulnessResponseSchema>;
export const UpdatePostUsefulnessResponseSchema = PostUsefulnessSchema;

export type CommentEntityType = Infer<typeof CommentEntityTypeSchema>;
export const CommentEntityTypeSchema = z.enum(["post", "comment"]);

export const commentBody = LocalizationsSchema.min(1);

export type Comment = Infer<typeof CommentSchema>;
export const CommentSchema = z.object({
    id,

    path: z.string().nonempty(),

    author: AuthorSchema.nullable(),

    isAnonymous: z.boolean(),
    anonimityReason: z.string().nonempty().nullable(),

    rating: RatingSchema,

    body: commentBody.nullable(),

    childrenCount: z.number().int().nonnegative(),

    createdAt: z.date(),
    updatedAt: z.date(),

    deletedAt: z.date().nullable(),
    deleteReason: z.string().nonempty().nullable(),
});

export type GetCommentsRequest = Infer<typeof GetCommentsRequestSchema>;
export const GetCommentsRequestSchema = z.object({
    entityType: CommentEntityTypeSchema,
    entityId: id,
});

export type GetCommentsResponse = Infer<typeof GetCommentsResponseSchema>;
export const GetCommentsResponseSchema = z.object({
    items: z.array(CommentSchema),
    total: z.number().int().nonnegative(),
});

export type CreateCommentRequest = Infer<typeof CreateCommentRequestSchema>;
export const CreateCommentRequestSchema = z.object({
    entityType: CommentEntityTypeSchema,
    entityId: id,

    body: commentBody,
});

export type UpdateCommentRequest = Infer<typeof UpdateCommentRequestSchema>;
export const UpdateCommentRequestSchema = z.object({
    body: commentBody,
});

export type DeleteCommentRequest = Infer<typeof DeleteCommentRequestSchema>;
export const DeleteCommentRequestSchema = z.object({
    reason: z.string().nonempty().nullable(),
});

export type UpdateCommentRatingRequest = Infer<typeof UpdateCommentRatingRequestSchema>;
export const UpdateCommentRatingRequestSchema = z.object({
    type: RatingTypeSchema,
});

export type UpdateCommentRatingResponse = Infer<typeof UpdateCommentRatingResponseSchema>;
export const UpdateCommentRatingResponseSchema = RatingSchema;

export type AnonimifyCommentRequest = Infer<typeof AnonimifyCommentRequestSchema>;
export const AnonimifyCommentRequestSchema = z.object({
    reason: z.string().nonempty().nullable(),
});

export type CreateLensRequest = Infer<typeof CreateLensRequestSchema>;
export const CreateLensRequestSchema = z.object({
    name: z.string().nonempty(),
    code: z.string().nonempty(),
});

export type UpdateLensRequest = Infer<typeof UpdateLensRequestSchema>;
export const UpdateLensRequestSchema = z
    .object({
        name: z.string().nonempty(),
        code: z.string().nonempty(),
    })
    .partial();
