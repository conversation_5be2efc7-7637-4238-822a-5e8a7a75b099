"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactorController = void 0;
const common_1 = require("@nestjs/common");
const zod_1 = require("../../zod");
const current_user_decorator_1 = require("../../auth/http/current-user.decorator");
const session_auth_guard_1 = require("../../auth/http/session-auth.guard");
const reactor_service_1 = require("../reactor.service");
const api_1 = require("@commune/api");
let ReactorController = class ReactorController {
    constructor(reactorService) {
        this.reactorService = reactorService;
    }
    async getPosts(pagination, user) {
        const response = await this.reactorService.getPosts(pagination, user);
        return api_1.Common.parseInput(api_1.Reactor.GetPostsResponseSchema, response);
    }
    async getPost(id, user) {
        const post = await this.reactorService.getPost(id, user);
        return api_1.Common.parseInput(api_1.Reactor.PostSchema, post);
    }
    async createPost(body, user) {
        return await this.reactorService.createPost(body, user);
    }
    async updatePost(id, body, user) {
        return await this.reactorService.updatePost(id, body, user);
    }
    async updatePostRating(id, body, user) {
        const newRating = await this.reactorService.updatePostRating(id, body, user);
        return api_1.Common.parseInput(api_1.Reactor.UpdatePostRatingResponseSchema, newRating);
    }
    async updatePostUsefulness(id, body, user) {
        const newUsefulness = await this.reactorService.updatePostUsefulness(id, body, user);
        return api_1.Common.parseInput(api_1.Reactor.UpdatePostUsefulnessResponseSchema, newUsefulness);
    }
    async deletePost(id, body, user) {
        return await this.reactorService.deletePost(id, body, user);
    }
    async getComments(body, user) {
        const response = await this.reactorService.getComments(body, user);
        return api_1.Common.parseInput(api_1.Reactor.GetCommentsResponseSchema, response);
    }
    async getComment(id, user) {
        const comment = await this.reactorService.getComment({ id }, user);
        return api_1.Common.parseInput(api_1.Reactor.CommentSchema, comment);
    }
    async createComment(body, user) {
        return await this.reactorService.createComment(body, user);
    }
    async updateComment(id, body, user) {
        return await this.reactorService.updateComment(id, body, user);
    }
    async updateCommentRating(id, body, user) {
        const newRating = await this.reactorService.updateCommentRating(id, body, user);
        return api_1.Common.parseInput(api_1.Reactor.UpdateCommentRatingResponseSchema, newRating);
    }
    async anonimifyComment(id, body, user) {
        return await this.reactorService.anonimifyComment(id, body, user);
    }
    async deleteComment(id, body, user) {
        return await this.reactorService.deleteComment(id, body, user);
    }
    async getLenses(user) {
        return await this.reactorService.getLenses(user);
    }
    async createLens(body, user) {
        return await this.reactorService.createLens(body, user);
    }
    async updateLens(id, body, user) {
        return await this.reactorService.updateLens(id, body, user);
    }
    async deleteLens(id, user) {
        return await this.reactorService.deleteLens(id, user);
    }
};
exports.ReactorController = ReactorController;
__decorate([
    (0, common_1.Get)("post"),
    __param(0, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Common.PaginationSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getPosts", null);
__decorate([
    (0, common_1.Get)("post/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getPost", null);
__decorate([
    (0, common_1.Post)("post"),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.CreatePostRequestSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "createPost", null);
__decorate([
    (0, common_1.Put)("post/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.UpdatePostRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updatePost", null);
__decorate([
    (0, common_1.Post)("post/:id/rating"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.UpdatePostRatingRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updatePostRating", null);
__decorate([
    (0, common_1.Post)("post/:id/usefulness"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.UpdatePostUsefulnessRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updatePostUsefulness", null);
__decorate([
    (0, common_1.Delete)("post/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.DeletePostRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "deletePost", null);
__decorate([
    (0, common_1.Get)("comment"),
    __param(0, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Reactor.GetCommentsRequestSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getComments", null);
__decorate([
    (0, common_1.Get)("comment/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getComment", null);
__decorate([
    (0, common_1.Post)("comment"),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.CreateCommentRequestSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "createComment", null);
__decorate([
    (0, common_1.Put)("comment/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.UpdateCommentRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updateComment", null);
__decorate([
    (0, common_1.Post)("comment/:id/rating"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.UpdateCommentRatingRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updateCommentRating", null);
__decorate([
    (0, common_1.Put)("comment/:id/anonimify"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.AnonimifyCommentRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "anonimifyComment", null);
__decorate([
    (0, common_1.Delete)("comment/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.DeleteCommentRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "deleteComment", null);
__decorate([
    (0, common_1.Get)("lens"),
    __param(0, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getLenses", null);
__decorate([
    (0, common_1.Post)("lens"),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.CreateLensRequestSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "createLens", null);
__decorate([
    (0, common_1.Put)("lens/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Reactor.UpdateLensRequestSchema))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updateLens", null);
__decorate([
    (0, common_1.Delete)("lens/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "deleteLens", null);
exports.ReactorController = ReactorController = __decorate([
    (0, common_1.Controller)("reactor"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    __metadata("design:paramtypes", [reactor_service_1.ReactorService])
], ReactorController);
//# sourceMappingURL=reactor.controller.js.map