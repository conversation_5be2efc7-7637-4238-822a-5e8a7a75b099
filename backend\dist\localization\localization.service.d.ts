import { PrismaService } from "src/prisma/prisma.service";
import { Prisma, Localization } from "@prisma/client";
import { Common } from "@commune/api";
export declare class LocalizationService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    protected getFirstNonNullValue(localizations: Normalize<Pick<Localization, "locale" | "value">>[], locales: Common.LocalizationLocale[]): string | null;
    getOne(id: string): Promise<{
        value: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
        locale: import("@prisma/client").$Enums.Locale;
    } | null>;
    getMany(where: Prisma.LocalizationWhereInput, pagination?: {
        page: number;
        size: number;
    }): Promise<{
        value: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
        locale: import("@prisma/client").$Enums.Locale;
    }[]>;
    createOne(data: Prisma.LocalizationCreateInput): Promise<{
        value: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
        locale: import("@prisma/client").$Enums.Locale;
    }>;
    createMany(data: Prisma.LocalizationCreateManyInput[]): Promise<Prisma.BatchPayload>;
    updateOne(id: string, data: Prisma.LocalizationUpdateInput): Promise<{
        value: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
        locale: import("@prisma/client").$Enums.Locale;
    }>;
    updateMany(where: Prisma.LocalizationWhereInput, data: Prisma.LocalizationUpdateInput): Promise<Prisma.BatchPayload>;
    softDeleteOne(id: string): Promise<{
        value: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
        locale: import("@prisma/client").$Enums.Locale;
    }>;
    softDeleteMany(where: Prisma.LocalizationWhereInput): Promise<Prisma.BatchPayload>;
    deleteOne(id: string): Promise<{
        value: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
        locale: import("@prisma/client").$Enums.Locale;
    }>;
    deleteMany(where: Prisma.LocalizationWhereInput): Promise<Prisma.BatchPayload>;
}
