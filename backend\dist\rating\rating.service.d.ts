import type { Common } from "@commune/api";
import type { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
export declare class RatingService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    getUserSummary(userId: string): Promise<{
        rating: number;
        karma: number;
        rate: number | null;
    }>;
    getKarmaPoints(userId: string, pagination?: Common.Pagination): Promise<({
        sourceUser: {
            name: {
                value: string;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import("@prisma/client").$Enums.Locale;
            }[];
            images: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            referrerId: string | null;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
            deletedAt: Date | null;
        };
        comment: {
            value: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
        }[];
    } & {
        targetUserId: string;
        quantity: number;
        id: string;
        sourceUserId: string;
        createdAt: Date;
        updatedAt: Date;
    })[]>;
    spendKarmaPoint(data: {
        sourceUserId: string;
        targetUserId: string;
        quantity: number;
        comment: Common.Localizations;
    }, user: CurrentUser): Promise<{
        id: string;
    }>;
    getUserFeedbacks(userId: string, pagination?: Common.Pagination): Promise<({
        sourceUser: {
            name: {
                value: string;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import("@prisma/client").$Enums.Locale;
            }[];
            images: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            referrerId: string | null;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
            deletedAt: Date | null;
        };
        text: {
            value: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
        }[];
    } & {
        targetUserId: string;
        value: number;
        id: string;
        sourceUserId: string;
        createdAt: Date;
        updatedAt: Date;
        isAnonymous: boolean;
    })[]>;
    createUserFeedback(data: {
        sourceUserId: string;
        targetUserId: string;
        value: number;
        isAnonymous: boolean;
        text: Common.Localizations;
    }, user: CurrentUser): Promise<{
        id: string;
    }>;
}
