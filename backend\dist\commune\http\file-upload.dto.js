"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileUploadSchema = exports.MAX_FILES_COUNT = exports.MAX_FILE_SIZE = exports.ALLOWED_FILE_TYPES = void 0;
const zod_1 = require("zod");
exports.ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];
exports.MAX_FILE_SIZE = 5 * 1024 * 1024;
exports.MAX_FILES_COUNT = 10;
exports.FileUploadSchema = zod_1.z.object({
    mimetype: zod_1.z.enum(exports.ALLOWED_FILE_TYPES),
    size: zod_1.z.number().max(exports.MAX_FILE_SIZE),
    originalname: zod_1.z.string(),
    buffer: zod_1.z.instanceof(Buffer),
});
//# sourceMappingURL=file-upload.dto.js.map