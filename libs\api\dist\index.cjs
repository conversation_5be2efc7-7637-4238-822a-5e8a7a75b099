"use strict";Object.defineProperty(exports, "__esModule", {value: true});var __defProp = Object.defineProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};

// src/common.ts
var common_exports = {};
__export(common_exports, {
  FormDataToObject: () => FormDataToObject,
  ImageSchema: () => ImageSchema,
  ImagesSchema: () => ImagesSchema,
  JsonStringToObject: () => JsonStringToObject,
  LocalizationLocaleSchema: () => LocalizationLocaleSchema,
  LocalizationLocalesSchema: () => LocalizationLocalesSchema,
  LocalizationSchema: () => LocalizationSchema,
  LocalizationsSchema: () => LocalizationsSchema,
  ObjectWithIdSchema: () => ObjectWithIdSchema,
  PaginationSchema: () => PaginationSchema,
  WebsiteLocaleSchema: () => WebsiteLocaleSchema,
  email: () => email,
  id: () => id,
  pagination: () => pagination,
  parseInput: () => parseInput,
  parseUnknown: () => parseUnknown,
  stringToDate: () => stringToDate
});
var _zod = require('zod');
var id = _zod.z.string().nanoid();
var email = _zod.z.string().email();
var stringToDate = _zod.z.union([_zod.z.number(), _zod.z.string(), _zod.z.date()]).pipe(_zod.z.coerce.date());
function JsonStringToObject(schema) {
  return _zod.z.string().transform((value) => JSON.parse(value)).pipe(_zod.z.object(schema));
}
function FormDataToObject(schema) {
  return _zod.z.object({
    data: JsonStringToObject(schema)
  });
}
var ObjectWithIdSchema = _zod.z.object({ id });
var WebsiteLocaleSchema = _zod.z.enum(["en", "ru"]);
var LocalizationLocaleSchema = _zod.z.enum(["en", "ru"]);
var LocalizationLocalesSchema = _zod.z.array(LocalizationLocaleSchema).min(1);
var LocalizationSchema = _zod.z.object({
  locale: LocalizationLocaleSchema,
  value: _zod.z.string().nonempty()
});
var LocalizationsSchema = _zod.z.array(LocalizationSchema);
var ImageSchema = _zod.z.object({
  id,
  url: _zod.z.string(),
  createdAt: stringToDate,
  updatedAt: stringToDate
});
var ImagesSchema = _zod.z.array(ImageSchema);
var pagination = {
  offset: _zod.z.coerce.number().int().default(0),
  limit: _zod.z.coerce.number().int().positive().max(100).default(20),
  page: _zod.z.coerce.number().int().positive().default(1),
  size: _zod.z.coerce.number().int().positive().max(100).default(20)
};
var PaginationSchema = _zod.z.object({
  page: pagination.page,
  size: pagination.size
});
function parseInput(schema, value) {
  return schema.parse(value);
}
function parseUnknown(schema, value) {
  return schema.parse(value);
}

// src/auth.ts
var auth_exports = {};
__export(auth_exports, {
  GetMeResponseSchema: () => GetMeResponseSchema,
  LoginRequestSchema: () => LoginRequestSchema,
  RegisterRequestSchema: () => RegisterRequestSchema,
  SendOtpRequestSchema: () => SendOtpRequestSchema,
  SendOtpResponseSchema: () => SendOtpResponseSchema,
  SuccessfulAuthResponseSchema: () => SuccessfulAuthResponseSchema,
  otp: () => otp
});


// src/user.ts
var user_exports = {};
__export(user_exports, {
  AuthorSchema: () => AuthorSchema,
  GetUserNoteResponseSchema: () => GetUserNoteResponseSchema,
  SetUserNoteRequestSchema: () => SetUserNoteRequestSchema,
  UpdateUserRequestSchema: () => UpdateUserRequestSchema,
  UpdateUserTitleRequestSchema: () => UpdateUserTitleRequestSchema,
  UserRoleSchema: () => UserRoleSchema,
  UserSchema: () => UserSchema,
  UserTitleSchema: () => UserTitleSchema,
  UserTitlesSchema: () => UserTitlesSchema,
  UsersSchema: () => UsersSchema,
  userDescription: () => userDescription,
  userName: () => userName,
  userNoteText: () => userNoteText,
  userTitleColor: () => userTitleColor,
  userTitleIsActive: () => userTitleIsActive
});

var userName = LocalizationsSchema;
var userDescription = LocalizationsSchema;
var userTitleIsActive = _zod.z.boolean();
var userTitleColor = _zod.z.string().nonempty().nullable();
var userNoteText = _zod.z.string().nonempty();
var UserRoleSchema = _zod.z.enum([
  "admin",
  "moderator",
  "user"
]);
var AuthorSchema = _zod.z.object({
  id,
  email,
  name: userName,
  images: ImagesSchema
});
var UserSchema = _zod.z.object({
  id,
  email,
  role: UserRoleSchema,
  name: userName,
  description: userDescription,
  images: ImagesSchema,
  createdAt: _zod.z.date(),
  updatedAt: _zod.z.date()
});
var UsersSchema = _zod.z.array(UserSchema);
var UpdateUserRequestSchema = _zod.z.object({
  name: userName,
  description: userDescription
}).partial();
var UserTitleSchema = _zod.z.object({
  id,
  ownerId: id.nullable(),
  isActive: userTitleIsActive,
  color: userTitleColor,
  createdAt: _zod.z.date(),
  updatedAt: _zod.z.date()
});
var UserTitlesSchema = _zod.z.array(UserTitleSchema);
var UpdateUserTitleRequestSchema = _zod.z.object({
  isActive: userTitleIsActive,
  color: userTitleColor
}).partial();
var GetUserNoteResponseSchema = _zod.z.object({
  text: userNoteText.nullable()
});
var SetUserNoteRequestSchema = _zod.z.object({
  text: userNoteText.nullable()
});

// src/auth.ts
var otp = _zod.z.string().nonempty().length(6);
var SendOtpRequestSchema = _zod.z.object({
  email
});
var SendOtpResponseSchema = _zod.z.object({
  isSent: _zod.z.boolean()
});
var GetMeResponseSchema = _zod.z.object({
  id,
  email,
  role: UserRoleSchema,
  name: userName,
  description: userDescription,
  images: ImagesSchema,
  createdAt: _zod.z.date(),
  updatedAt: _zod.z.date()
});
var RegisterRequestSchema = _zod.z.object({
  referrerId: id.nullable(),
  email,
  otp
});
var LoginRequestSchema = _zod.z.object({
  email,
  otp
});
var SuccessfulAuthResponseSchema = _zod.z.object({
  id,
  email,
  role: UserRoleSchema
});

// src/commune.ts
var commune_exports = {};
__export(commune_exports, {
  CommuneInvitationSchema: () => CommuneInvitationSchema,
  CommuneInvitationStatusSchema: () => CommuneInvitationStatusSchema,
  CommuneInvitationsSchema: () => CommuneInvitationsSchema,
  CommuneJoinRequestSchema: () => CommuneJoinRequestSchema,
  CommuneJoinRequestStatusSchema: () => CommuneJoinRequestStatusSchema,
  CommuneJoinRequestsSchema: () => CommuneJoinRequestsSchema,
  CommuneMemberSchema: () => CommuneMemberSchema,
  CommuneMemberTypeSchema: () => CommuneMemberTypeSchema,
  CommuneMembersSchema: () => CommuneMembersSchema,
  CommuneSchema: () => CommuneSchema,
  CommunesSchema: () => CommunesSchema,
  CreateCommuneInvitationRequestSchema: () => CreateCommuneInvitationRequestSchema,
  CreateCommuneJoinRequestRequestSchema: () => CreateCommuneJoinRequestRequestSchema,
  CreateCommuneMemberRequestSchema: () => CreateCommuneMemberRequestSchema,
  CreateCommuneRequestSchema: () => CreateCommuneRequestSchema,
  TransferHeadStatusRequestSchema: () => TransferHeadStatusRequestSchema,
  UpdateCommuneRequestSchema: () => UpdateCommuneRequestSchema,
  communeDescription: () => communeDescription,
  communeMemberActorType: () => communeMemberActorType,
  communeMemberName: () => communeMemberName,
  communeName: () => communeName
});

var CommuneMemberTypeSchema = _zod.z.enum(["user"]);
var communeMemberActorType = CommuneMemberTypeSchema;
var communeMemberName = LocalizationsSchema;
var CommuneMemberSchema = _zod.z.object({
  id,
  actorType: communeMemberActorType,
  actorId: id,
  name: communeMemberName,
  images: ImagesSchema,
  joinedAt: _zod.z.date(),
  leftAt: _zod.z.date().nullable()
});
var CommuneMembersSchema = _zod.z.array(CommuneMemberSchema);
var communeName = LocalizationsSchema;
var communeDescription = LocalizationsSchema;
var CommuneSchema = _zod.z.object({
  id,
  name: LocalizationsSchema,
  description: LocalizationsSchema,
  headMember: _zod.z.object({
    actorType: communeMemberActorType,
    actorId: id,
    name: communeMemberName
  }),
  memberCount: _zod.z.number().int().positive(),
  images: ImagesSchema,
  createdAt: _zod.z.date(),
  updatedAt: _zod.z.date()
});
var CommunesSchema = _zod.z.array(CommuneSchema);
var CreateCommuneRequestSchema = JsonStringToObject({
  headUserId: id.optional(),
  name: communeName,
  description: communeDescription
  // Images are handled separately via file upload
});
var UpdateCommuneRequestSchema = _zod.z.object({
  name: communeName,
  description: communeDescription
});
var CreateCommuneMemberRequestSchema = _zod.z.object({
  userId: id
});
var CommuneInvitationStatusSchema = _zod.z.enum(["pending", "accepted", "rejected", "expired"]);
var CommuneInvitationSchema = _zod.z.object({
  id,
  communeId: id,
  userId: id,
  status: CommuneInvitationStatusSchema,
  createdAt: _zod.z.date(),
  updatedAt: _zod.z.date()
});
var CommuneInvitationsSchema = _zod.z.array(CommuneInvitationSchema);
var CreateCommuneInvitationRequestSchema = _zod.z.object({
  communeId: id,
  userId: id
});
var CommuneJoinRequestStatusSchema = _zod.z.enum(["pending", "accepted", "rejected"]);
var CommuneJoinRequestSchema = _zod.z.object({
  id,
  communeId: id,
  userId: id,
  status: CommuneJoinRequestStatusSchema,
  createdAt: _zod.z.date(),
  updatedAt: _zod.z.date()
});
var CommuneJoinRequestsSchema = _zod.z.array(CommuneJoinRequestSchema);
var CreateCommuneJoinRequestRequestSchema = _zod.z.object({
  communeId: id,
  userId: id
});
var TransferHeadStatusRequestSchema = _zod.z.object({
  newHeadUserId: id
});

// src/reactor.ts
var reactor_exports = {};
__export(reactor_exports, {
  AnonimifyCommentRequestSchema: () => AnonimifyCommentRequestSchema,
  AuthorSchema: () => AuthorSchema2,
  CommentEntityTypeSchema: () => CommentEntityTypeSchema,
  CommentSchema: () => CommentSchema,
  CreateCommentRequestSchema: () => CreateCommentRequestSchema,
  CreateLensRequestSchema: () => CreateLensRequestSchema,
  CreatePostRequestSchema: () => CreatePostRequestSchema,
  DeleteCommentRequestSchema: () => DeleteCommentRequestSchema,
  DeletePostRequestSchema: () => DeletePostRequestSchema,
  GetCommentsRequestSchema: () => GetCommentsRequestSchema,
  GetCommentsResponseSchema: () => GetCommentsResponseSchema,
  GetPostsRequestSchema: () => GetPostsRequestSchema,
  GetPostsResponseSchema: () => GetPostsResponseSchema,
  PostSchema: () => PostSchema,
  PostUsefulnessSchema: () => PostUsefulnessSchema,
  RatingSchema: () => RatingSchema,
  RatingTypeSchema: () => RatingTypeSchema,
  UpdateCommentRatingRequestSchema: () => UpdateCommentRatingRequestSchema,
  UpdateCommentRatingResponseSchema: () => UpdateCommentRatingResponseSchema,
  UpdateCommentRequestSchema: () => UpdateCommentRequestSchema,
  UpdateLensRequestSchema: () => UpdateLensRequestSchema,
  UpdatePostRatingRequestSchema: () => UpdatePostRatingRequestSchema,
  UpdatePostRatingResponseSchema: () => UpdatePostRatingResponseSchema,
  UpdatePostRequestSchema: () => UpdatePostRequestSchema,
  UpdatePostUsefulnessRequestSchema: () => UpdatePostUsefulnessRequestSchema,
  UpdatePostUsefulnessResponseSchema: () => UpdatePostUsefulnessResponseSchema,
  commentBody: () => commentBody,
  postBody: () => postBody,
  postTags: () => postTags,
  postTitle: () => postTitle,
  postUsefulness: () => postUsefulness
});

var postUsefulness = _zod.z.number().int().min(0).max(10);
var AuthorSchema2 = _zod.z.object({
  id,
  name: userName,
  avatar: _zod.z.string().nullable()
});
var RatingTypeSchema = _zod.z.enum(["like", "dislike"]);
var RatingSchema = _zod.z.object({
  likes: _zod.z.number().int().nonnegative(),
  dislikes: _zod.z.number().int().nonnegative(),
  status: RatingTypeSchema.nullable()
});
var PostUsefulnessSchema = _zod.z.object({
  value: postUsefulness.nullable(),
  count: _zod.z.number().int().nonnegative(),
  totalValue: _zod.z.number().min(0).max(10).nullable()
});
var postTitle = LocalizationsSchema.min(1);
var postBody = LocalizationsSchema.min(1);
var postTags = _zod.z.array(id);
var PostSchema = _zod.z.object({
  id,
  author: AuthorSchema2,
  rating: RatingSchema,
  usefulness: PostUsefulnessSchema,
  title: postTitle,
  body: postBody,
  tags: postTags,
  createdAt: _zod.z.date(),
  updatedAt: _zod.z.date()
});
var GetPostsRequestSchema = _zod.z.object({
  paginationSchema: PaginationSchema
});
var GetPostsResponseSchema = _zod.z.object({
  items: _zod.z.array(PostSchema),
  total: _zod.z.number().int().nonnegative()
});
var CreatePostRequestSchema = _zod.z.object({
  title: postTitle,
  body: postBody,
  tags: postTags
});
var UpdatePostRequestSchema = _zod.z.object({
  title: postTitle,
  body: postBody,
  tags: postTags
}).partial();
var DeletePostRequestSchema = _zod.z.object({
  reason: _zod.z.string().nonempty().nullable()
});
var UpdatePostRatingRequestSchema = _zod.z.object({
  type: RatingTypeSchema
});
var UpdatePostRatingResponseSchema = RatingSchema;
var UpdatePostUsefulnessRequestSchema = _zod.z.object({
  value: postUsefulness.nullable()
});
var UpdatePostUsefulnessResponseSchema = PostUsefulnessSchema;
var CommentEntityTypeSchema = _zod.z.enum(["post", "comment"]);
var commentBody = LocalizationsSchema.min(1);
var CommentSchema = _zod.z.object({
  id,
  path: _zod.z.string().nonempty(),
  author: AuthorSchema2.nullable(),
  isAnonymous: _zod.z.boolean(),
  anonimityReason: _zod.z.string().nonempty().nullable(),
  rating: RatingSchema,
  body: commentBody.nullable(),
  childrenCount: _zod.z.number().int().nonnegative(),
  createdAt: _zod.z.date(),
  updatedAt: _zod.z.date(),
  deletedAt: _zod.z.date().nullable(),
  deleteReason: _zod.z.string().nonempty().nullable()
});
var GetCommentsRequestSchema = _zod.z.object({
  entityType: CommentEntityTypeSchema,
  entityId: id
});
var GetCommentsResponseSchema = _zod.z.object({
  items: _zod.z.array(CommentSchema),
  total: _zod.z.number().int().nonnegative()
});
var CreateCommentRequestSchema = _zod.z.object({
  entityType: CommentEntityTypeSchema,
  entityId: id,
  body: commentBody
});
var UpdateCommentRequestSchema = _zod.z.object({
  body: commentBody
});
var DeleteCommentRequestSchema = _zod.z.object({
  reason: _zod.z.string().nonempty().nullable()
});
var UpdateCommentRatingRequestSchema = _zod.z.object({
  type: RatingTypeSchema
});
var UpdateCommentRatingResponseSchema = RatingSchema;
var AnonimifyCommentRequestSchema = _zod.z.object({
  reason: _zod.z.string().nonempty().nullable()
});
var CreateLensRequestSchema = _zod.z.object({
  name: _zod.z.string().nonempty(),
  code: _zod.z.string().nonempty()
});
var UpdateLensRequestSchema = _zod.z.object({
  name: _zod.z.string().nonempty(),
  code: _zod.z.string().nonempty()
}).partial();

// src/rating.ts
var rating_exports = {};
__export(rating_exports, {
  CreateUserFeedbackRequestSchema: () => CreateUserFeedbackRequestSchema,
  GetKarmaPointsResponseSchema: () => GetKarmaPointsResponseSchema,
  GetUserFeedbacksResponseSchema: () => GetUserFeedbacksResponseSchema,
  GetUserSummaryResponseSchema: () => GetUserSummaryResponseSchema,
  SpendKarmaPointRequestSchema: () => SpendKarmaPointRequestSchema,
  karmaPointComment: () => karmaPointComment,
  karmaPointQuantity: () => karmaPointQuantity,
  userFeedbackText: () => userFeedbackText,
  userFeedbackValue: () => userFeedbackValue
});

var karmaPointQuantity = _zod.z.number().int();
var karmaPointComment = LocalizationsSchema.min(1);
var GetKarmaPointsResponseSchema = _zod.z.array(
  _zod.z.object({
    id,
    author: AuthorSchema,
    quantity: karmaPointQuantity,
    comment: karmaPointComment
  })
);
var SpendKarmaPointRequestSchema = _zod.z.object({
  sourceUserId: id,
  targetUserId: id,
  quantity: karmaPointQuantity,
  comment: karmaPointComment
});
var userFeedbackValue = _zod.z.number().int().min(0).max(10);
var userFeedbackText = LocalizationsSchema.min(1);
var GetUserFeedbacksResponseSchema = _zod.z.array(
  _zod.z.object({
    id,
    author: AuthorSchema.nullable(),
    isAnonymous: _zod.z.boolean(),
    value: userFeedbackValue,
    text: userFeedbackText
  })
);
var CreateUserFeedbackRequestSchema = _zod.z.object({
  sourceUserId: id,
  targetUserId: id,
  value: userFeedbackValue,
  isAnonymous: _zod.z.boolean(),
  text: userFeedbackText
});
var GetUserSummaryResponseSchema = _zod.z.object({
  rating: _zod.z.number().int(),
  karma: _zod.z.number().int(),
  rate: _zod.z.number().min(0).max(10).nullable()
});







exports.Auth = auth_exports; exports.Common = common_exports; exports.Commune = commune_exports; exports.Rating = rating_exports; exports.Reactor = reactor_exports; exports.User = user_exports;
//# sourceMappingURL=index.cjs.map