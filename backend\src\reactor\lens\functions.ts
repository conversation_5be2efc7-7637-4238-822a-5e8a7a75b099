import type { tokenize as Tokenize } from "./tokenize.mjs" with { "resolution-mode": "import" };
import type { createAst as CreateAst } from "./create-ast.mjs" with { "resolution-mode": "import" };
import type { validate as Validate } from "./validate.mjs" with { "resolution-mode": "import" };
import type { generateSql as GenerateSql } from "./generate-sql.mjs" with { "resolution-mode": "import" };

let tokenize: typeof Tokenize;
let createAst: typeof CreateAst;
let validate: typeof Validate;
let generateSql: typeof GenerateSql;

import("./tokenize.mjs").then((module) => {
    tokenize = module.tokenize;
});

import("./create-ast.mjs").then((module) => {
    createAst = module.createAst;
});

import("./validate.mjs").then((module) => {
    validate = module.validate;
});

import("./generate-sql.mjs").then((module) => {
    generateSql = module.generateSql;
});

export { tokenize, createAst, validate, generateSql };
